module.exports = {
    // 1.一行代码的最大字符数，默认是80(printWidth: <int>)
    printWidth: 120,
    // 2.tab宽度为2空格(tabWidth: <int>)
    tabWidth: 4,
    // 3.是否使用tab来缩进，我们使用空格(useTabs: <bool>)
    useTabs: false,
    // 4.结尾是否添加分号，false的情况下只会在一些导致ASI错误的其工况下在开头加分号，我选择无分号结尾的风格(semi: <bool>)
    semi: true,
    // 5.使用单引号(singleQuote: <bool>)
    singleQuote: true,
    // 超宽换行
    proseWrap: 'preserve',
    // 箭头函数一个参数是否使用括号包裹参数
    arrowParens: 'avoid',
    // 花括号内填充空格 { a: 23 }
    bracketSpacing: true,
    // 换行标识:auto/lf
    endOfLine: 'lf',
    // 在jsx中使用单引号代替双引号
    jsxSingleQuote: false,
    // 行尾逗号
    trailingComma: 'none',
    // props 是否用 引号 包裹
    quoteProps: 'as-needed',
    // 格式化代码的区间
    // "rangeStart": 0,
    //"rangeEnd": 9999,
    // 解析文件类型
    //parser: 'babel-ts',
    // 仅格式化带有 @format 、 @prettier 头的文件
    requirePragma: false,
    // 插入 @format 在文件头部
    insertPragma: false,
    // strict是所有的空格换行情况都保留，ignore的话就是所有元素间的空格都会被忽略
    // eg. <span> sss </span>
    htmlWhitespaceSensitivity: 'strict',
    // just for vue
    vueIndentScriptAndStyle: false,
    // 用于 Markdown 等，自动语言识别
    embeddedLanguageFormatting: 'auto',
    singleAttributePerLine: false
};
