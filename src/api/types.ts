/**
 * API业务层类型定义
 */

// 基础分页参数类型
export interface PaginationParams {
    page?: number;
    limit?: number;
    size?: number;
    current?: number;
    pageSize?: number;
}

// 基础分页响应类型
export interface PaginationResponse<T> {
    list: T[];
    total: number;
    page: number;
    limit: number;
    pages: number;
}

// 基础查询参数类型
export interface BaseQuery extends PaginationParams {
    keyword?: string;
    status?: number | string;
    startTime?: string;
    endTime?: string;
    sort?: string;
    order?: 'asc' | 'desc';
}

// 用户相关类型
export interface User {
    id: number;
    username: string;
    nickname: string;
    email: string;
    phone: string;
    avatar: string;
    status: number;
    roleId: number;
    roleName: string;
    tenantId: number;
    tenantCode: string;
    createTime: string;
    updateTime: string;
    lastLoginTime?: string;
    lastLoginIp?: string;
}

export interface LoginParams {
    username: string;
    password: string;
    captcha?: string;
    captchaKey?: string;
    remember?: boolean;
}

export interface LoginResponse {
    token: string;
    refreshToken: string;
    user: User;
    permissions: string[];
    roles: string[];
    menus: MenuItem[];
}

// 菜单相关类型
export interface MenuItem {
    id: number;
    parentId: number;
    title: string;
    name: string;
    path: string;
    component: string;
    icon: string;
    type: 'menu' | 'button' | 'api';
    sort: number;
    status: number;
    permission: string;
    children?: MenuItem[];
    meta?: {
        title: string;
        icon: string;
        hidden: boolean;
        cache: boolean;
        affix: boolean;
        breadcrumb: boolean;
    };
}

// 角色相关类型
export interface Role {
    id: number;
    name: string;
    code: string;
    description: string;
    status: number;
    permissions: string[];
    createTime: string;
    updateTime: string;
}

// 权限相关类型
export interface Permission {
    id: number;
    parentId: number;
    name: string;
    code: string;
    type: 'menu' | 'button' | 'api';
    path: string;
    method: string;
    description: string;
    status: number;
    children?: Permission[];
}

// 租户相关类型
export interface Tenant {
    id: number;
    code: string;
    name: string;
    description: string;
    status: number;
    expireTime: string;
    maxUsers: number;
    currentUsers: number;
    createTime: string;
    updateTime: string;
    contact: {
        name: string;
        phone: string;
        email: string;
    };
}

// 系统配置相关类型
export interface SystemConfig {
    id: number;
    key: string;
    value: string;
    description: string;
    type: 'string' | 'number' | 'boolean' | 'json';
    group: string;
    sort: number;
    status: number;
    createTime: string;
    updateTime: string;
}

// 通知相关类型
export interface Notification {
    id: number;
    title: string;
    content: string;
    type: 'info' | 'success' | 'warning' | 'error';
    status: 'unread' | 'read';
    userId: number;
    createTime: string;
    readTime?: string;
    extra?: Record<string, any>;
}

// 监控相关类型
export interface SystemMonitor {
    cpu: {
        usage: number;
        cores: number;
        model: string;
    };
    memory: {
        total: number;
        used: number;
        free: number;
        usage: number;
    };
    disk: {
        total: number;
        used: number;
        free: number;
        usage: number;
    };
    network: {
        upload: number;
        download: number;
    };
    timestamp: number;
}

// 文件上传相关类型
export interface UploadFile {
    id: number;
    originalName: string;
    fileName: string;
    filePath: string;
    fileSize: number;
    mimeType: string;
    hash: string;
    userId: number;
    createTime: string;
}

export interface UploadResponse {
    file: UploadFile;
    url: string;
}

// 操作日志相关类型
export interface OperationLog {
    id: number;
    userId: number;
    username: string;
    operation: string;
    method: string;
    params: string;
    result: string;
    ip: string;
    userAgent: string;
    executeTime: number;
    createTime: string;
}

// 通用响应类型
export interface ApiResult<T = any> {
    code: number;
    message: string;
    data: T;
    success: boolean;
    timestamp: number;
}

// 通用列表响应类型
export type ApiListResult<T> = ApiResult<PaginationResponse<T>>;

// 通用操作响应类型
export type ApiOperationResult = ApiResult<boolean>;

// 字典数据类型
export interface DictData {
    label: string;
    value: string | number;
    type?: string;
    status?: number;
    sort?: number;
    remark?: string;
}

// 树形数据类型
export interface TreeNode {
    id: number;
    parentId: number;
    label: string;
    value: string | number;
    children?: TreeNode[];
    disabled?: boolean;
    [key: string]: any;
}

// API接口类型定义
export interface AccountAPI {
    sendYzm: any;
    ping: any;
    login: any;
    logout: any;
    menu: any;
    info: any;
    edit: any;
    logs: any;
    changePassword: any;
    lockscreen: any;
    unLockscreen: any;
    refreshToken: any;
    verifyToken: any;
}

export interface CommonAPI {
    system: any;
    sentences: any;
    upload: any;
    uploadFile: any;
    exportFile: any;
    importFile: any;
    file: any;
    docs: any;
}

export interface SystemAPI {
    app: any;
    user: any;
    role: any;
    menu: any;
    department: any;
    onlineUser: any;
    log: any;
    dic: any;
}
