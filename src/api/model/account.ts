import http from '@/utils/request';

// 账户相关接口参数类型
export interface SendYzmParams {
    phone?: string;
    email?: string;
    type: 'sms' | 'email';
}

export interface ChangePasswordParams {
    oldPassword?: string;
    newPassword: string;
    confirmPassword: string;
    force?: boolean;
}

export interface LockscreenParams {
    password: string;
}

export interface RefreshTokenParams {
    refreshToken: string;
}

export interface VerifyTokenParams {
    token: string;
}

const accountAPI = {
    /**
     * 验证码发送接口
     */
    sendYzm: {
        url: `/system/account/sendyzm`,
        name: '发送验证码',
        post: async function (data: SendYzmParams = { type: 'sms' }) {
            return await http.post(this.url, data);
        }
    },
    ping: {
        url: `/system/account/ping`,
        name: '检测用户在线状态',
        get: async function (data: any = {}) {
            return await http.post(this.url, data, { silent: true });
        }
    },
    /**
     * 账号登录接口
     */
    login: {
        url: `/system/account/login`,
        name: '登录获取ACCESS_TOKEN',
        post: async function (data: any) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 退出登录接口
     */
    logout: {
        url: `/system/account/logout`,
        name: '退出登录',
        post: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 获取用户菜单接口
     */
    menu: {
        url: `/system/account/menu`,
        name: '获取用户菜单',
        get: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 获取用户基本信息接口
     */
    info: {
        url: `/system/account/info`,
        name: '验证用户是否登录（也可用作获取用户基本信息）',
        get: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 更新用户基本信息接口
     */
    edit: {
        url: `/system/account/edit`,
        name: '更新用户基本信息',
        post: async function (data: any) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 获取用户操作日志接口
     */
    logs: {
        url: `/system/account/logs`,
        name: '操作日志',
        get: async function (data: any = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 强制修改密码是不需要传入原密码
     * 主动修改密码需要验证原密码
     */
    changePassword: {
        url: `/system/account/changePassword`,
        name: '重置密码',
        post: async function (data: ChangePasswordParams) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 服务端锁屏接口
     */
    lockscreen: {
        url: `/system/account/lockscreen`,
        name: '锁定屏幕',
        post: async function (data: LockscreenParams) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 锁屏解锁接口
     */
    unLockscreen: {
        url: `/system/account/unLockscreen`,
        name: '解锁屏幕',
        post: async function (data: LockscreenParams) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 刷新用户token接口
     */
    refreshToken: {
        url: `/system/account/refreshToken`,
        name: '刷新用户token',
        post: async function (data: RefreshTokenParams) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 验证token合法性接口
     */
    verifyToken: {
        url: `/system/account/verifyToken`,
        name: '验证token合法性',
        post: async function (data: VerifyTokenParams) {
            return await http.post(this.url, data);
        }
    }
};

export default accountAPI;
