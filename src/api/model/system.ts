import http from '@/utils/request';

const systemAPI = {
    app: {
        store: {
            url: `/system/appModule/store`,
            name: '应用市场数据获取',
            get: async function (params?: any) {
                return await http.post(this.url, params);
            }
        },
        list: {
            url: `/system/appModule/list`,
            name: '应用列表数据获取',
            get: async function (params?: any) {
                return await http.post(this.url, params);
            }
        }
    },
    setting: {
        groupListData: {
            url: `/system/setting/groupListData`,
            name: '获取全部分组和对应数据',
            get: async function (params?: any) {
                return await http.get(this.url, params);
            }
        },
        saveData: {
            url: `/system/setting/saveData`,
            name: '保存配置数据',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        }
    },
    configureGroup: {
        list: {
            url: `/system/configureGroup/list`,
            name: '获取全部分组数据',
            get: async function (params?: any) {
                return await http.get(this.url, params);
            }
        },
        add: {
            url: `/system/configureGroup/add`,
            name: '添加分组',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        edit: {
            url: `/system/configureGroup/edit`,
            name: '编辑分组',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        delete: {
            url: `/system/configureGroup/delete`,
            name: '分组软删除',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        status: {
            url: `/system/configureGroup/status`,
            name: '设置分组状态（禁用启用）',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        hidden: {
            url: `/system/configureGroup/hidden`,
            name: '设置分组隐藏（隐藏显示）',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        sort: {
            url: `/system/configureGroup/sort`,
            name: '设置分组排序',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        }
    },
    configure: {
        widgets: {
            url: `/system/configure/widgets`,
            name: '控件类型获取方法',
            get: async function (params?: any) {
                return await http.get(this.url, params);
            }
        },
        list: {
            url: `/system/configure/list`,
            name: '获取全部配置项数据',
            get: async function (params?: any) {
                return await http.get(this.url, params);
            }
        },
        add: {
            url: `/system/configure/add`,
            name: '添加配置项',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        edit: {
            url: `/system/configure/edit`,
            name: '编辑配置项',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        delete: {
            url: `/system/configure/delete`,
            name: '配置项软删除',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        status: {
            url: `/system/configure/status`,
            name: '设置配置项状态（禁用启用）',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        }
    },
    user: {
        list: {
            url: `/system/user/list`,
            name: '获取用户列表',
            get: async function (params?: any) {
                return await http.post(this.url, params);
            }
        },
        add: {
            url: `/system/user/add`,
            name: '添加用户',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        edit: {
            url: `/system/user/edit`,
            name: '编辑用户',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        delete: {
            url: `/system/user/delete`,
            name: '用户软删除',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        status: {
            url: `/system/user/status`,
            name: '设置用户状态（禁用启用）',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        resetPassword: {
            url: `/system/user/resetPassword`,
            name: '批量重置密码',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        setDepts: {
            url: `/system/user/setDepts`,
            name: '批量分配部门',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        setRoles: {
            url: `/system/user/setRoles`,
            name: '批量分配角色',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        selects: {
            url: `/system/admin/selects`,
            name: '获取所有用户列表for下拉筛选',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        }
    },
    role: {
        list: {
            url: `/system/role/list`,
            name: '获取角色列表',
            get: async function (params?: any) {
                return await http.post(this.url, params);
            }
        },
        add: {
            url: `/system/role/add`,
            name: '添加角色',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        edit: {
            url: `/system/role/edit`,
            name: '编辑角色',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        delete: {
            url: `/system/role/delete`,
            name: '角色软删除',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        access: {
            url: `/system/role/access`,
            name: '角色组节点授权',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        sort: {
            url: `/system/role/sort`,
            name: '设置角色排序',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        }
    },
    menu: {
        list: {
            url: `/system/permission/list`,
            name: '获取节点列表',
            get: async function () {
                return await http.post(this.url);
            }
        },
        add: {
            url: `/system/permission/add`,
            name: '添加节点',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        edit: {
            url: `/system/permission/edit`,
            name: '编辑节点',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        delete: {
            url: `/system/permission/delete`,
            name: '节点软删除',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        status: {
            url: `/system/permission/status`,
            name: '设置节点状态（禁用启用）',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        }
    },
    department: {
        list: {
            url: `/system/department/list`,
            name: '获取部门列表',
            get: async function (params?: any) {
                return await http.get(this.url, params);
            }
        },
        add: {
            url: `/system/department/add`,
            name: '添加部门',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        edit: {
            url: `/system/department/edit`,
            name: '编辑部门',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        delete: {
            url: `/system/department/delete`,
            name: '部门软删除',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        status: {
            url: `/system/department/status`,
            name: '设置部门状态（禁用启用）',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        sort: {
            url: `/system/department/sort`,
            name: '设置部门排序',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        }
    },
    onlineUser: {
        list: {
            url: `/system/onlineUser/list`,
            name: '获取在线用户',
            get: async function (params?: any) {
                return await http.post(this.url, params);
            }
        },
        kickOut: {
            url: `/system/onlineUser/kickOut`,
            name: '踢出用户',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        block: {
            url: `/system/onlineUser/block`,
            name: '拉黑用户',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        blackList: {
            url: `/system/onlineUser/blackList`,
            name: '黑名单列表',
            get: async function (params?: any) {
                return await http.post(this.url, params);
            }
        },
        batchKickOut: {
            url: `/system/onlineUser/batchKickOut`,
            name: '批量踢出用户',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        batchBlock: {
            url: `/system/onlineUser/batchBlock`,
            name: '批量拉黑用户',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        removeBlock: {
            url: `/system/onlineUser/removeBlock`,
            name: '移除黑名单',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        }
    },
    log: {
        list: {
            url: `/system/log/list`,
            name: '日志列表',
            get: async function (params?: any) {
                return await http.post(this.url, params);
            }
        }
    },
    dic: {
        tree: {
            url: `/system/dic/tree`,
            name: '获取字典树',
            get: async function () {
                return await http.get(this.url);
            }
        },
        list: {
            url: `/system/dic/list`,
            name: '字典明细',
            get: async function (params?: any) {
                return await http.get(this.url, params);
            }
        },
        get: {
            url: `/system/dic/get`,
            name: '获取字典数据',
            get: async function (params: any) {
                return await http.get(this.url, params);
            }
        }
    }
};

export default systemAPI;
