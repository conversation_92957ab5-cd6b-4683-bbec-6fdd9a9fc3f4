import http from '@/utils/request';

const tenantAPI = {
    manage: {
        list: {
            url: `/tenant/manage/list`,
            name: '获取租户列表',
            get: async function (params: any) {
                try {
                    return await http.get(this.url, params);
                } catch (e) {
                    console.error('租户列表请求失败:', e);
                    return { code: 500, message: '租户列表获取失败，请检查网络连接' };
                }
            }
        },
        add: {
            url: `/tenant/manage/add`,
            name: '添加租户',
            post: async function (params: any) {
                try {
                    return await http.post(this.url, params);
                } catch (e) {
                    console.error('接口请求异常:', e);
                    return { code: 500, message: '操作失败，请稍后重试' };
                }
            }
        },
        edit: {
            url: `/tenant/manage/edit`,
            name: '编辑租户',
            post: async function (params: any) {
                try {
                    return await http.post(this.url, params);
                } catch (e) {
                    console.error('接口请求异常:', e);
                    return { code: 500, message: '操作失败，请稍后重试' };
                }
            }
        },
        delete: {
            url: `/tenant/manage/delete`,
            name: '租户软删除',
            post: async function (params: any) {
                try {
                    return await http.post(this.url, params);
                } catch (e) {
                    console.error('接口请求异常:', e);
                    return { code: 500, message: '操作失败，请稍后重试' };
                }
            }
        },
        batchDelete: {
            url: `/tenant/manage/batchDelete`,
            name: '批量删除租户',
            post: async function (params: any) {
                try {
                    return await http.post(this.url, params);
                } catch (e) {
                    console.error('接口请求异常:', e);
                    return { code: 500, message: '操作失败，请稍后重试' };
                }
            }
        },
        status: {
            url: `/tenant/manage/status`,
            name: '设置租户状态（禁用启用）',
            post: async function (params: any) {
                try {
                    return await http.post(this.url, params);
                } catch (e) {
                    console.error('接口请求异常:', e);
                    return { code: 500, message: '操作失败，请稍后重试' };
                }
            }
        },
        sort: {
            url: `/tenant/manage/sort`,
            name: '设置租户排序',
            post: async function (params: any) {
                try {
                    return await http.post(this.url, params);
                } catch (e) {
                    console.error('接口请求异常:', e);
                    return { code: 500, message: '操作失败，请稍后重试' };
                }
            }
        }
    },
    menu: {
        list: {
            url: `/tenant/menu/list`,
            name: '获取节点列表',
            get: async function () {
                return await http.post(this.url);
            }
        },
        add: {
            url: `/tenant/menu/add`,
            name: '添加节点',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        edit: {
            url: `/tenant/menu/edit`,
            name: '编辑节点',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        delete: {
            url: `/tenant/menu/delete`,
            name: '节点软删除',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        },
        status: {
            url: `/tenant/menu/status`,
            name: '设置节点状态（禁用启用）',
            post: async function (params: any) {
                return await http.post(this.url, params);
            }
        }
    }
};

export default tenantAPI;
