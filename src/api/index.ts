/**
 * @description 自动import导入所有 api 模块
 */

// 使用动态导入保持与JavaScript版本一致
const modules: Record<string, any> = {};
const metas = import.meta.glob('./model/*.ts', { import: 'default', eager: true }) as Record<string, any>;

for (let key in metas) {
    let k = key.replace('model/', '');
    modules[k.replace(/(\.\/|\.ts)/g, '')] = metas[key];
}

// 导出类型
export type { 
    // 基础类型
    ApiResult,
    ApiListResult,
    PaginationParams,
    BaseQuery,
    
    // 业务类型
    User,
    Role,
    Permission,
    MenuItem,
    Tenant,
    SystemConfig,
    Notification,
    OperationLog,
    
    // API接口类型
    AccountAPI,
    CommonAPI,
    SystemAPI
} from './types';

export default modules;
