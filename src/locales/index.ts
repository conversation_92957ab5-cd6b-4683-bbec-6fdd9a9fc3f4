import type { AppConfig } from '@/config';
import config from '@/config';
import type { StorageConfig } from '@/config/storage';
import storageConfig from '@/config/storage';
import type { Tool } from '@/utils/tool';
import tool from '@/utils/tool';
import { createI18n } from 'vue-i18n';
import type { Language } from 'element-plus/lib/locale';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import enCn from 'element-plus/dist/locale/en.mjs';
import zh_cn from './lang/zh-cn.js';
import en from './lang/en.js';

// 语言消息类型定义
interface LocaleMessage {
    [key: string]: any;
}

// 支持的语言类型
type SupportedLocale = 'zh-cn' | 'en';

// 消息结构类型
interface Messages {
    [K in SupportedLocale]: {
        el: Language;
    } & LocaleMessage;
}

const messages: Messages = {
    'zh-cn': {
        el: zhCn,
        ...zh_cn
    },
    en: {
        el: enCn,
        ...en
    }
};

const i18n = createI18n({
    locale: (tool.data.get(storageConfig.vars.appLang) || config.APP_LANG) as SupportedLocale,
    fallbackLocale: 'zh-cn' as SupportedLocale,
    globalInjection: true,
    messages
});

export default i18n;
export type { SupportedLocale, LocaleMessage, Messages };
