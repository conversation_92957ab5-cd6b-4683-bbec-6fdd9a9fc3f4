import config from '@/config';
import storageConfig from '@/config/storage';
import tool from '@/utils/tool';
import { createI18n } from 'vue-i18n';
import type { Language } from 'element-plus/es/locale';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import enCn from 'element-plus/es/locale/lang/en';
import zh_cn from './lang/zh-cn';
import en from './lang/en';

// 语言消息类型定义
interface LocaleMessage {
    [key: string]: any;
}

// 支持的语言类型
type SupportedLocale = 'zh-cn' | 'en';

// 消息结构类型
type Messages = {
    [K in SupportedLocale]: {
        el: Language;
    } & LocaleMessage;
};

const messages: Messages = {
    'zh-cn': {
        el: zhCn,
        ...zh_cn
    },
    en: {
        el: enCn,
        ...en
    }
};

const i18n = createI18n({
    locale: (tool.data.get(storageConfig.vars.appLang) || config.APP_LANG) as SupportedLocale,
    fallbackLocale: 'zh-cn' as SupportedLocale,
    globalInjection: true,
    messages: messages as any
});

export default i18n;
export type { SupportedLocale, LocaleMessage, Messages };
