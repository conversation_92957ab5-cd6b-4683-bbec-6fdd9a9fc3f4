// 登录相关文本类型
interface LoginMessages {
    slogan: string;
    describe: string;
    signInTitle: string;
    accountLogin: string;
    mobileLogin: string;
    rememberMe: string;
    forgetPassword: string;
    signIn: string;
    signInOther: string;
    userPlaceholder: string;
    userError: string;
    PWPlaceholder: string;
    PWError: string;
    admin: string;
    user: string;
    mobilePlaceholder: string;
    mobileError: string;
    smsPlaceholder: string;
    smsError: string;
    smsGet: string;
    smsSent: string;
    noAccount: string;
    createAccount: string;
    wechatLoginTitle: string;
    wechatLoginMsg: string;
    wechatLoginResult: string;
}

// 用户相关文本类型
interface UserMessages {
    dynamic: string;
    info: string;
    settings: string;
    theme_mode: string;
    theme_mode_msg: string;
    language: string;
    language_msg: string;
}

// 中文语言包类型
interface ZhCnMessages {
    login: LoginMessages;
    user: UserMessages;
}

const zhCnMessages: ZhCnMessages = {
    login: {
        slogan: '高性能 / 精致 / 优雅',
        describe: '让技术更普惠，让开发更简单',
        signInTitle: '用户登录',
        accountLogin: '账号登录',
        mobileLogin: '手机号登录',
        rememberMe: '24小时免登录',
        forgetPassword: '忘记密码',
        signIn: '登录',
        signInOther: '其他登录方式',
        userPlaceholder: '用户名 / 手机 / 邮箱',
        userError: '请输入用户名',
        PWPlaceholder: '请输入密码',
        PWError: '请输入密码',
        admin: '管理员',
        user: '用户',
        mobilePlaceholder: '手机号码',
        mobileError: '请输入手机号码',
        smsPlaceholder: '短信验证码',
        smsError: '请输入短信验证码',
        smsGet: '获取验证码',
        smsSent: '已发送短信至手机号码',
        noAccount: '还没有账号?',
        createAccount: '创建新账号',
        wechatLoginTitle: '二维码登录',
        wechatLoginMsg: '请使用微信扫一扫登录 | 模拟3秒后自动扫描',
        wechatLoginResult: '已扫描 | 请在设备中点击授权登录'
    },
    user: {
        dynamic: '近期动态',
        info: '个人信息',
        settings: '设置',
        theme_mode: '主题模式',
        theme_mode_msg: '指定系统中使用的主题模式',
        language: '语言',
        language_msg: '翻译进行中，暂翻译了本视图的文本'
    }
};

export default zhCnMessages;
export type { LoginMessages, UserMessages, ZhCnMessages };
