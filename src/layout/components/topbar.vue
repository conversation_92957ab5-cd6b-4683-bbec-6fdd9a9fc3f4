<template>
    <div class="layout-topbar" v-if="this.appLayout === 'default'">
        <div class="left-panel">
            <div class="quick-menus">
                <el-dropdown
                    class="panel-item"
                    :class="{ 'is-active': isDropdownVisible }"
                    trigger="hover"
                    :show-timeout="0"
                    @command="handleMenu"
                    @visible-change="handleDropdownVisible"
                >
                    <div>
                        <el-icon class="el-icon--left" style="margin-right: 3px; top: 2px">
                            <sc-icon-quick />
                        </el-icon>
                        <span>快捷操作</span>
                        <el-icon class="el-icon--right">
                            <el-icon-caret-bottom />
                        </el-icon>
                    </div>
                    <template #dropdown>
                        <div class="custom-dropdown-container">
                            <el-scrollbar max-height="350px" class="quick-menu-scrollbar">
                                <div class="scrollable-menu-items">
                                    <template v-if="quickMenus.length">
                                        <template v-for="menu in quickMenus" :key="menu.id">
                                            <el-dropdown-item :command="{ type: 'navigate', path: menu.path, openType: menu.openType || '_blank' }">
                                                {{ menu.title }}
                                            </el-dropdown-item>
                                        </template>
                                    </template>
                                    <div v-else class="empty-state">
                                        <el-icon><sc-icon-template-log /></el-icon>
                                        <span>暂无快捷菜单</span>
                                    </div>
                                </div>
                            </el-scrollbar>
                            <div class="menu-divider"></div>
                            <el-dropdown-item command="quickMenuManage">
                                <el-icon><el-icon-setting /></el-icon>
                                <span>快捷菜单管理</span>
                            </el-dropdown-item>
                        </div>
                    </template>
                </el-dropdown>
                <div class="panel-item" @click="openApiDoc" v-if="config.SHOW_API_DOCS">
                    <el-icon class="el-icon--left"><sc-icon-apidoc /></el-icon>
                    <span>API文档</span>
                </div>
                <!--<el-breadcrumb class="hidden-sm-and-down" separator-icon="el-icon-arrow-right">
                    <transition-group name="breadcrumb">
                        <template v-for="item in breadList" :key="item.title">
                            <el-breadcrumb-item
                                v-if="item.path != '/' && !item.meta.hiddenBreadcrumb"
                                :key="item.meta.title"
                                ><el-icon v-if="item.meta.icon" class="icon"><component :is="item.meta.icon" /></el-icon
                                >{{ item.meta.title }}</el-breadcrumb-item
                            >
                        </template>
                    </transition-group>
                </el-breadcrumb>-->
            </div>
        </div>
        <div class="center-panel"></div>
        <div class="right-panel">
            <slot></slot>
        </div>
    </div>
    <quick-menu-dialog ref="quickMenuDialog" @update:menus="updateQuickMenus" />
</template>

<script>
import quickMenuDialog from './quickMenu/dialog.vue';
import quickMenuModel from './quickMenu/model';
import config from '@/config';

export default {
    components: {
        quickMenuDialog
    },
    data() {
        return {
            breadList: [],
            quickMenus: [],
            isDropdownVisible: false,
            config
        };
    },
    props: {
        appLayout: {
            type: String,
            default: () => null
        }
    },
    async created() {
        await this.loadQuickMenus();
    },
    watch: {
        $route() {
            //this.getBreadcrumb();
        }
    },
    methods: {
        /**
         * 加载快捷菜单
         */
        async loadQuickMenus() {
            try {
                this.quickMenus = await quickMenuModel.getQuickMenus();
                console.log(this.quickMenus)
            } catch (error) {
                console.error('快捷菜单加载失败:', error);
                this.quickMenus = [];
            }
        },

        /**
         * 个人操作
         * @param command
         */
        handleMenu(command) {
            if (command === 'quickMenuManage') {
                this.$refs.quickMenuDialog.show();
            } else if (command.type === 'navigate') {
                let url = command.path;
                if (!url.startsWith('http')) {
                    url = window.location.origin + url;
                }
                if (command.openType === '_blank') {
                    window.open(url, '_blank');
                } else {
                    this.$router.push(command.path);
                }
            }
        },

        /**
         * 下拉菜单显示状态
         * @param visible
         */
        handleDropdownVisible(visible) {
            this.isDropdownVisible = visible;
        },

        /**
         * 更新快捷菜单
         * @param menus
         */
        updateQuickMenus(menus) {
            this.quickMenus = menus;
        },

        /**
         * 面包屑
         */
        getBreadcrumb() {
            this.breadList = this.$route.meta.breadcrumb;
        },

        /**
         * 打开API文档
         */
        openApiDoc() {
            this.$router.push({ path: '/document/index' });
        }
    }
};
</script>

<style lang="scss" scoped>
// 重置父级line-height影响高度不一致
.el-dropdown {
    line-height: inherit;
}
.quick-menus {
    align-items: center;
    display: flex;
    height: 100%;

    @media screen and (max-width: 768px) {
        display: none;
    }

    .panel-item {
        align-items: center;
        cursor: pointer;
        display: flex;
        height: 100%;
        border-radius: var(--main-header-item-radius);
        padding: var(--main-header-item-padding);

        &:hover, &.is-active {
            background: var(--main-header-item-hover-bg);
        }

        .el-icon--right {
            line-height: inherit;
            margin-left: 2px;
        }
    }

    /*.user-avatar {
        align-items: center;
        display: flex;
        height: 49px;

        label {
            cursor: pointer;
            display: inline-block;
            margin-left: 5px;
        }
    }*/
}

.custom-dropdown-container {
    display: flex;
    flex-direction: column;
    padding: 10px 0;
}

.menu-divider {
    height: 1px;
    background-color: #ebeef5;
    margin: 5px 0;
}

.empty-state {
    padding: 20px;
    text-align: center;
    color: #aaa;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100px;

    .el-icon {
        font-size: 24px;
        margin-bottom: 12px;
    }

    span {
        font-size: 12px;
        line-height: 1.4;
    }
}
</style>
