<template>
    <div class="layout-tabs" ref="layoutTabs">
        <ul ref="tabs">
            <li
                v-for="tab in tabList"
                v-bind:key="tab"
                :class="[isActive(tab) ? 'active' : '', tab.meta.affix ? 'affix' : '', tab.meta.notOnTab ? 'hide' : '']"
                @contextmenu.prevent="openContextMenu($event, tab)"
            >
                <router-link :to="tab">
                    <i class="el-icon tab-icon"><component :is="tab.meta.icon"></component></i>
                    <span class="tab-title">{{ tab.meta.title }}</span>
                    <el-icon class="tab-close" v-if="!tab.meta.affix" @click.prevent.stop="handleTabClose($event, tab)"
                        ><el-icon-close
                    /></el-icon>
                </router-link>
            </li>
        </ul>

        <!-- 添加更多标签按钮和下拉菜单 -->
        <div v-show="showMoreButton" class="tabs-more">
            <el-dropdown trigger="click" @command="handleTabClick">
                <el-button type="primary" link>
                    <el-icon><el-icon-more /></el-icon>
                </el-button>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item
                            v-for="tab in tabList"
                            :key="tab.fullPath"
                            :command="tab"
                            :class="{'is-active': isActive(tab)}"
                        >
                            <el-icon v-if="tab.meta.icon"><component :is="tab.meta.icon" /></el-icon>
                            <span>{{ tab.meta.title }}</span>
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
    </div>

    <transition name="el-zoom-in-top">
        <ul
            v-if="contextMenuVisible"
            id="contextmenu"
            :style="{ left: left + 'px', top: top + 'px' }"
            class="contextmenu"
        >
            <li @click="refreshTab()">
                <el-icon><el-icon-refresh /></el-icon>刷新
            </li>
            <hr />
            <li :class="contextMenuItem.meta.affix ? 'disabled' : ''" @click="closeTabs()">
                <el-icon><el-icon-close /></el-icon>关闭标签
            </li>
            <li @click="closeOtherTabs()">
                <el-icon><el-icon-folder-delete /></el-icon>关闭其他标签
            </li>
            <hr />
            <li @click="maximize()">
                <el-icon><el-icon-full-screen /></el-icon>最大化
            </li>
            <li @click="openWindow()">
                <el-icon><el-icon-copy-document /></el-icon>在新的窗口中打开
            </li>
        </ul>
    </transition>
</template>

<script>
import Sortable from 'sortablejs';
import { useKeepAliveStore } from '@/stores/keepAlive';
import { useIframeStore } from '@/stores/iframe';
import { useViewTabsStore } from '@/stores/viewTabs';
import tool from '@/utils/tool.js';
import config from '@/config';

const keepAliveStore = useKeepAliveStore();
const iframeStore = useIframeStore();
const viewTabsStore = useViewTabsStore();

// 使用全局配置
const MAX_TABS = config.MAX_TABS;

// 扩展viewTabsStore，添加检查方法
viewTabsStore.canAddTab = function(route) {
    // 如果MAX_TABS为0，表示不限制标签数量
    if (MAX_TABS === 0) return true;

    // 固定标签和已存在的标签总是可以添加
    if (route.meta && route.meta.affix) return true;

    // 检查是否已经存在该标签
    const existingTab = this.viewTabs.find(tab => tab.fullPath === route.fullPath);
    if (existingTab) return true;

    // 检查是否达到上限
    return this.viewTabs.length < MAX_TABS;
};

export default {
    name: 'mainTabs',
    data() {
        return {
            contextMenuVisible: false,
            contextMenuItem: null,
            left: 0,
            top: 0,
            tabList: viewTabsStore.viewTabs,
            tipDisplayed: false,
            resizeObserver: null,
            maxTabs: MAX_TABS, // 使用全局常量
            isDragging: false, // 跟踪是否正在拖动
            lastDragTime: 0,   // 记录上次拖动结束时间
            showMoreButton: false,
        };
    },
    props: {},
    watch: {
        $route(e) {
            this.addViewTabs(e);
            //判断标签容器是否出现滚动条
            this.$nextTick(() => {
                const tabs = this.$refs.tabs;
                if (tabs && tabs.scrollWidth > tabs.clientWidth) {
                    //确保当前标签在可视范围内
                    let targetTab = tabs.querySelector('.active');
                    targetTab.scrollIntoView();
                    //显示提示
                    if (!this.tipDisplayed) {
                        this.$msgbox({
                            type: 'warning',
                            center: true,
                            title: '提示',
                            message: '当前标签数量过多，可通过鼠标滚轴滚动标签栏。关闭标签数量可减少系统性能消耗。',
                            confirmButtonText: '知道了'
                        });
                        this.tipDisplayed = true;
                    }
                }
            });
        },
        contextMenuVisible(value) {
            var _this = this;
            var cm = function (e) {
                let sp = document.getElementById('contextmenu');
                if (sp && !sp.contains(e.target)) {
                    _this.closeMenu();
                }
            };
            if (value) {
                document.body.addEventListener('click', e => cm(e));
            } else {
                document.body.removeEventListener('click', e => cm(e));
            }
        },
        tabList: {
            handler() {
                this.$nextTick(() => {
                    this.checkShowMoreButton();
                });
            },
            deep: true
        }
    },
    created() {
        const menu = this.$router.sc_getMenu();
        const dashboardRoute = this.treeFind(menu, node => node.path === this.$CONFIG.DASHBOARD_URL);
        if (dashboardRoute) {
            dashboardRoute.fullPath = dashboardRoute.path;
            this.addViewTabs(dashboardRoute);
            this.addViewTabs(this.$route);
        }
    },
    mounted() {
        this.tabDrop();
        this.scrollInit();
        this.initResizeObserver();
        this.checkShowMoreButton();
        window.addEventListener('resize', this.checkShowMoreButton);
    },
    beforeUnmount() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
        // 移除wheel事件监听器
        if (this._wheelHandler && this.$refs.tabs) {
            this.$refs.tabs.removeEventListener('wheel', this._wheelHandler, { passive: false });
        }
        window.removeEventListener('resize', this.checkShowMoreButton);
    },
    methods: {
        //查找树
        treeFind(tree, func) {
            for (const data of tree) {
                if (func(data)) return data;
                if (data.children) {
                    const res = this.treeFind(data.children, func);
                    if (res) return res;
                }
            }
            return null;
        },
        //标签拖拽排序
        tabDrop() {
            const target = this.$refs.tabs;
            const _this = this; // 保存this引用

            Sortable.create(target, {
                draggable: 'li:not(.affix)',  // 不允许拖拽有affix类的标签
                animation: 300,
                filter: '.affix',  // 过滤掉affix类的标签
                direction: 'horizontal', // 只允许水平方向拖动
                delay: 150, // 延迟启动拖动，防止意外触发
                delayOnTouchOnly: true, // 仅在触摸设备上启用延迟
                ghostClass: 'tab-ghost', // 拖动时原位置的占位样式
                dragClass: 'tab-drag', // 拖动中元素的样式
                forceFallback: true, // 强制使用自定义拖动行为而非浏览器默认行为
                fallbackClass: 'tab-fallback', // 拖动时克隆元素的样式
                fallbackOnBody: true, // 将克隆元素附加到body上
                scroll: true, // 允许容器滚动
                scrollSensitivity: 80, // 滚动触发区域大小
                scrollSpeed: 10, // 滚动速度
                setData: function(dataTransfer, dragEl) {
                    // 自定义拖动数据，可以帮助改善拖动行为
                    dataTransfer.setData('text', dragEl.textContent);
                    // 设置拖动图像
                    const img = new Image();
                    img.src = 'data:image/svg+xml,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" width="1" height="1"><rect width="1" height="1" fill="transparent"/></svg>');
                    dataTransfer.setDragImage(img, 0, 0);
                },
                onStart: function(evt) {
                    // 设置拖动状态
                    _this.isDragging = true;

                    // 拖动开始时添加一个拖动中的类到body
                    document.body.classList.add('dragging-tab');
                },
                onEnd: function(evt) {
                    // 拖动结束时移除拖动中的类
                    document.body.classList.remove('dragging-tab');

                    // 获取所有固定标签
                    const affixTabs = Array.from(target.querySelectorAll('li.affix'));

                    // 确保所有固定标签都在最前面
                    if (affixTabs.length > 0) {
                        // 将所有固定标签移到前面
                        affixTabs.forEach(tab => {
                            target.insertBefore(tab, target.firstChild);
                        });
                    }

                    // 延迟300ms后再将拖动状态设为false，防止误触发关闭按钮
                    _this.lastDragTime = Date.now();
                    setTimeout(() => {
                        _this.isDragging = false;
                    }, 300);
                },
                onMove: function(evt) {
                    // 阻止拖拽固定标签
                    return !evt.related.classList.contains('affix');
                }
            });
        },
        //增加tab
        addViewTabs(route) {
            if (route.name && !route.meta.fullpage) {
                // 检查标签页数量是否超过限制 - 由路由拦截器处理，这里不再显示错误提示
                if (!viewTabsStore.canAddTab(route)) {
                    return;
                }

                viewTabsStore.pushViewTabs(route);

                // 不缓存页面状态
                if (!route.meta.notKeepAlive) {
                    keepAliveStore.pushKeepLive(route.name);
                }
                // 确保新打开的标签页在可视区域内
                this.$nextTick(() => {
                    const activeTab = this.$refs.tabs.querySelector('.active');
                    if (activeTab) {
                        activeTab.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }
                });
            }
        },
        //高亮tab
        isActive(route) {
            return route.fullPath === this.$route.fullPath;
        },
        //关闭tab
        closeSelectedTab(tab, autoPushLatestView = true) {
            const currentTabIndex = this.tabList.findIndex(item => item.fullPath === tab.fullPath);
            viewTabsStore.removeViewTabs(tab);
            iframeStore.removeIframeList(tab);
            keepAliveStore.removeKeepLive(tab.name);
            if (autoPushLatestView && this.isActive(tab)) {
                const leftView = this.tabList[currentTabIndex - 1];
                if (leftView) {
                    this.$router.push(leftView);
                } else {
                    this.$router.push('/');
                }
            }
        },
        //tab右键
        openContextMenu(e, tab) {
            this.contextMenuItem = tab;
            this.contextMenuVisible = true;
            this.left = e.clientX + 1;
            this.top = e.clientY + 1;

            //FIX 右键菜单边缘化位置处理
            this.$nextTick(() => {
                let sp = document.getElementById('contextmenu');
                if (document.body.offsetWidth - e.clientX < sp.offsetWidth) {
                    this.left = document.body.offsetWidth - sp.offsetWidth + 1;
                    this.top = e.clientY + 1;
                }
            });
        },
        /**
         * 关闭右键菜单
         */
        closeMenu() {
            this.contextMenuItem = null;
            this.contextMenuVisible = false;
        },
        /**
         * TAB 刷新
         * @param activeTab
         */
        refreshTab(activeTab) {
            // 增加快捷键刷新tab操作
            const currentTab = this.contextMenuItem || activeTab;
            this.contextMenuVisible = false;
            //判断是否当前路由，否的话跳转
            if (this.$route.fullPath !== currentTab.fullPath) {
                this.$router.push({
                    path: currentTab.fullPath,
                    query: currentTab.query
                });
            }
            iframeStore.refreshIframe(currentTab);
            setTimeout(() => {
                keepAliveStore.removeKeepLive(currentTab.name);
                keepAliveStore.setRouteShow(false);
                this.$nextTick(() => {
                    keepAliveStore.pushKeepLive(currentTab.name);
                    keepAliveStore.setRouteShow(true);
                });
            }, 0);
        },
        //TAB 关闭
        closeTabs() {
            var currentTab = this.contextMenuItem;
            if (!currentTab.meta.affix) {
                this.closeSelectedTab(currentTab);
                this.contextMenuVisible = false;
            }
        },
        //TAB 关闭其他
        closeOtherTabs() {
            var currentTab = this.contextMenuItem;
            //判断是否当前路由，否的话跳转
            if (this.$route.fullPath !== currentTab.fullPath) {
                this.$router.push({
                    path: currentTab.fullPath,
                    query: currentTab.query
                });
            }
            var tabs = [...this.tabList];
            tabs.forEach(tab => {
                if ((tab.meta && tab.meta.affix) || currentTab.fullPath === tab.fullPath) {
                    return true;
                } else {
                    this.closeSelectedTab(tab, false);
                }
            });
            this.contextMenuVisible = false;
        },
        //TAB 最大化
        maximize() {
            var currentTab = this.contextMenuItem;
            this.contextMenuVisible = false;
            //判断是否当前路由，否的话跳转
            if (this.$route.fullPath !== currentTab.fullPath) {
                this.$router.push({
                    path: currentTab.fullPath,
                    query: currentTab.query
                });
            }
            document.getElementById('app').classList.add('main-maximize');
        },
        //新窗口打开
        openWindow() {
            var currentTab = this.contextMenuItem;
            var url = currentTab.href || '/';
            if (!currentTab.meta.affix) {
                this.closeSelectedTab(currentTab);
            }
            url = tool.setUrlParam(url, 'newWindow', 1);
            window.open(url);
            this.contextMenuVisible = false;
        },
        //横向滚动
        scrollInit() {
            const scrollDiv = this.$refs.tabs;
            const handler = (event) => {
                event.preventDefault();
                const detail = event.wheelDelta || event.detail;
                //火狐上滚键值-3 下滚键值3，其他内核上滚键值120 下滚键值-120
                const moveForwardStep = 1;
                const moveBackStep = -1;
                let step = 0;
                if (detail === 3 || (detail < 0 && detail !== -3)) {
                    step = moveForwardStep * 50;
                } else {
                    step = moveBackStep * 50;
                }
                scrollDiv.scrollLeft += step;
            };
            scrollDiv.addEventListener('wheel', handler, { passive: false });
            // 在组件销毁时移除事件监听器
            this._wheelHandler = handler;
        },
        // 初始化ResizeObserver
        initResizeObserver() {
            this.resizeObserver = new ResizeObserver(() => {
                const tabs = this.$refs.tabs;
                if (tabs && tabs.scrollWidth > tabs.clientWidth) {
                    const activeTab = tabs.querySelector('.active');
                    if (activeTab) {
                        activeTab.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }
                }
            });
            this.resizeObserver.observe(this.$refs.tabs);
        },
        /**
         * 处理标签关闭按钮点击
         * 带有防拖动误触保护
         */
        handleTabClose(e, tab) {
            // 阻止事件冒泡
            e.preventDefault();
            e.stopPropagation();

            // 如果正在拖动或者拖动刚结束不久，不执行关闭操作
            if (this.isDragging || (Date.now() - this.lastDragTime < 300)) {
                return;
            }

            // 正常关闭标签
            this.closeSelectedTab(tab);
        },
        // 处理下拉菜单点击
        handleTabClick(tab) {
            if (!this.isActive(tab)) {
                this.$router.push(tab);
            }
        },
        // 检查是否需要显示更多按钮
        checkShowMoreButton() {
            if (!this.$refs.tabs || !this.$refs.layoutTabs) return;

            const tabsWidth = this.$refs.tabs.scrollWidth;
            const containerWidth = this.$refs.layoutTabs.clientWidth;
            this.showMoreButton = tabsWidth > containerWidth;
        }
    }
};
</script>

<style lang="scss" scoped>
.contextmenu {
    position: fixed;
    width: 200px;
    margin: 0;
    border-radius: 0;
    background: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color-light);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 3000;
    list-style-type: none;
    padding: 10px 0;

    hr {
        margin: 5px 0;
        border: none;
        height: 1px;
        font-size: 0;
        background-color: var(--el-border-color-light);
    }

    li {
        display: flex;
        align-items: center;
        margin: 0;
        cursor: pointer;
        line-height: 30px;
        padding: 0 17px;
        color: #606266;

        i {
            font-size: 14px;
            margin-right: 10px;
        }

        &:hover {
            background-color: var(--el-fill-color-light);
            color: var(--el-text-color-regular);
        }

        &.disabled {
            cursor: not-allowed;
            color: #bbbbbb;
            background: transparent;
        }
    }
}

.tabs-tip {
    padding: 5px;

    p {
        margin-bottom: 10px;
    }
}

html[data-theme='dark'] {
    .contextmenu li {
        color: var(--el-text-color-primary);
    }
}

.layout-tabs {
    position: relative;
    //display: flex;
    //align-items: center;
    scrollbar-width: thin;

    &::-webkit-scrollbar {
        height: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 3px;
    }

    li.affix {
        cursor: default !important;  /* 对固定标签使用默认光标而非可拖动光标 */
        position: relative;
    }

    /* 拖动状态样式 */
    .tab-ghost {
        opacity: 0.5;
        background-color: var(--el-color-primary-light-9) !important;
        border: 1px dashed var(--el-color-primary) !important;
        border-bottom: none !important;

        a {
            /* 占位符内容透明 */
            opacity: 0.6;
        }
    }

    .tab-drag {
        opacity: 0.8;
    }

    /* 非固定标签可拖动样式 */
    li:not(.affix) {
        cursor: grab;

        &:active {
            cursor: grabbing;
        }
    }

    .tabs-more {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        background: var(--el-bg-color);
        border-left: 1px solid var(--el-border-color-dark);
        padding: 8px;
        z-index: 10;

        .el-dropdown {
            vertical-align: middle;
        }
    }

    ul {
        flex: 1;
        overflow-x: hidden;
        margin-right: 40px; // 为更多按钮预留空间
    }
}

/* 拖动时的克隆元素样式 */
.tab-fallback {
    display: none !important;
}

/* 拖动状态下的全局样式 */
body.dragging-tab {
    cursor: grabbing !important;

    /* 在拖动状态下禁用关闭按钮效果 */
    .layout-tabs .tab-close {
        pointer-events: none !important; /* 禁止鼠标事件 */
        opacity: 0.5 !important; /* 降低透明度表示禁用 */
    }
}

:deep(.el-dropdown-menu__item) {
    &.is-active {
        color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
    }

    .el-icon {
        margin-right: 5px;
        vertical-align: middle;
    }

    span {
        vertical-align: middle;
    }
}
</style>
