import { db } from '@/utils/indexedDB';
import storage from '@/config/storage';

// 快捷菜单项类型
export interface QuickMenuItem {
    id?: string | number;
    name: string;
    path: string;
    icon?: string;
    order?: number;
    createTime?: string;
    updateTime?: string;
    [key: string]: any;
}

// 存储对象名称
const STORE_NAME: string = storage.indexedDB.storeConfigs.quickMenu.name;

/**
 * 快捷菜单数据库模型
 */
class QuickMenuModel {
    // 初始化 Promise
    private initPromise: Promise<void> | null = null;

    /**
     * 构造函数
     */
    constructor() {
        console.log('初始化快捷菜单数据库模型');
        // 初始化 Promise
        this.initPromise = Promise.resolve();
    }

    /**
     * 获取快速菜单列表
     * @returns {Promise<QuickMenuItem[]>} 快速菜单列表
     */
    async getQuickMenus(): Promise<QuickMenuItem[]> {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            // 使用 db 的 getAll 方法获取所有快捷菜单
            const result: QuickMenuItem[] = await db.getAll(STORE_NAME);
            console.log(`获取快捷菜单成功，共 ${result.length} 条记录`);
            return result;
        } catch (error) {
            console.error('获取快捷菜单失败:', error);
            return [];
        }
    }

    /**
     * 保存快速菜单列表
     * @param {QuickMenuItem[]} items 快速菜单列表
     * @returns {Promise<boolean>} 是否成功
     */
    async saveQuickMenus(items: QuickMenuItem[]): Promise<boolean> {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            console.log(`批量保存快捷菜单，共 ${items.length} 条记录`);

            // 使用 db 的 saveAll 方法批量保存快捷菜单
            await db.saveAll(STORE_NAME, items);

            console.log(`批量保存快捷菜单成功`);
            return true;
        } catch (error) {
            console.error('保存快捷菜单失败:', error);
            return false;
        }
    }

    /**
     * 关闭数据库连接
     */
    close(): void {
        // 使用 closeDatabase 方法
        import('@/utils/indexedDB').then(({ closeDatabase }) => {
            closeDatabase();
        });
    }

    /**
     * 删除数据库
     * @returns {Promise<void>}
     */
    async deleteDatabase(): Promise<void> {
        try {
            // 使用 deleteDatabase 方法
            const { deleteDatabase } = await import('@/utils/indexedDB');
            await deleteDatabase();
        } catch (error) {
            console.error('删除数据库失败:', error);
            throw error;
        }
    }
}

// 创建数据库实例
const quickMenuModel = new QuickMenuModel();

// 导出数据库实例
export default quickMenuModel;
