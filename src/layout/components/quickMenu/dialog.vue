<template>
    <el-dialog
        v-model="dialogVisible"
        width="680px"
        :close-on-click-modal="false"
    >
        <template #title>
            <div style="display: flex; align-items: center; font-size:16px; font-weight: bold;">
                <el-icon style="margin-right: 5px;"><el-icon-setting /></el-icon>
                <span>快捷菜单管理</span>
            </div>
        </template>
        <el-table :data="menuList" style="width: 100%" height="400">
            <el-table-column prop="title" label="菜单名称" width="180">
                <template #default="scope">
                    <el-input v-model="scope.row.title" placeholder="请输入菜单名称" />
                </template>
            </el-table-column>
            <el-table-column prop="path" label="路由路径" width="180">
                <template #default="scope">
                    <el-input v-model="scope.row.path" placeholder="请输入路由路径" />
                </template>
            </el-table-column>
            <el-table-column prop="openType" label="打开方式" width="120">
                <template #default="scope">
                    <el-select v-model="scope.row.openType" class="w-full">
                        <el-option label="新窗口" value="_blank" />
                        <el-option label="当前窗口" value="_self" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
                <template #default="scope">
                    <el-button link type="primary" @click="moveUp(scope.$index)" :disabled="scope.$index === 0">
                        上移
                    </el-button>
                    <el-button link type="primary" @click="moveDown(scope.$index)" :disabled="scope.$index === menuList.length - 1">
                        下移
                    </el-button>
                    <el-button link type="danger" @click="deleteMenu(scope.$index)">
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <div style="display: flex; justify-content: space-between;">
                <div class="button-group">
                    <el-button type="primary" @click="addMenu" :disabled="menuList.length >= 20">
                        <el-icon><el-icon-plus /></el-icon>
                        添加菜单
                    </el-button>
                    <el-button @click="exportMenus">
                        <el-icon><el-icon-download /></el-icon>
                        导出
                    </el-button>
                    <el-upload
                        class="upload-demo"
                        action=""
                        :auto-upload="false"
                        :show-file-list="false"
                        accept=".json"
                        @change="handleImport"
                        style="display: inline-block"
                    >
                        <el-button>
                            <el-icon><el-icon-upload /></el-icon>
                            导入
                        </el-button>
                    </el-upload>
                </div>
                <div>
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="saveMenus">确定</el-button>
                </div>
            </div>
        </template>
    </el-dialog>
</template>

<script>
import quickMenuModel from './model';

export default {
    name: 'QuickMenuDialog',
    data() {
        return {
            dialogVisible: false,
            menuList: []
        }
    },
    methods: {
        async loadMenus() {
            try {
                this.menuList = await quickMenuModel.getQuickMenus();
            } catch (error) {
                this.$message.error('获取菜单失败');
                this.menuList = [];
            }
        },

        async saveMenus() {
            try {
                // 添加表单验证
                const invalidMenu = this.menuList.find(menu => !menu.title || !menu.path || !menu.openType);
                if (invalidMenu) {
                    this.$message.warning('菜单名称、路由路径和打开方式不能为空');
                    return;
                }

                await quickMenuModel.saveQuickMenus(this.menuList);
                this.$emit('update:menus', this.menuList);
                this.dialogVisible = false;
                this.$message.success('保存成功');
            } catch (error) {
                this.$message.error('保存失败');
            }
        },

        addMenu() {
            if (this.menuList.length >= 20) {
                this.$message.warning('最多只能添加20个快捷菜单');
                return;
            }
            this.menuList.push({
                title: '',
                path: '',
                openType: '_blank'  // 默认打开方式为新窗口
            });
        },

        deleteMenu(index) {
            this.menuList.splice(index, 1);
        },

        moveUp(index) {
            if (index > 0) {
                const temp = [...this.menuList];
                const item = temp[index];
                temp[index] = temp[index - 1];
                temp[index - 1] = item;
                this.menuList = temp;
            }
        },

        moveDown(index) {
            if (index < this.menuList.length - 1) {
                const temp = [...this.menuList];
                const item = temp[index];
                temp[index] = temp[index + 1];
                temp[index + 1] = item;
                this.menuList = temp;
            }
        },

        async show() {
            this.dialogVisible = true;
            await this.loadMenus();
        },

        exportMenus() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const fileName = `quickMenu_${year}${month}${day}${hours}${minutes}.json`;
            const data = JSON.stringify(this.menuList, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        },

        handleImport(file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const content = JSON.parse(e.target.result);
                    if (Array.isArray(content)) {
                        this.menuList = content;
                        this.$message.success('导入成功');
                    } else {
                        this.$message.error('导入失败：文件格式不正确');
                    }
                } catch (error) {
                    this.$message.error('导入失败：文件解析错误');
                }
            };
            reader.readAsText(file.raw);
        }
    }
}
</script>

<style lang="scss" scoped>
.el-button + .el-button {
    margin-left: 4px;
}
.w-full {
    width: 100%;
}
.button-group {
    display: flex;
    align-items: center;
    gap: 8px;
}
</style>
