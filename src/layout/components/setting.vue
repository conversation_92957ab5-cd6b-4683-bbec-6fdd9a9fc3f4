<template>
    <el-form ref="form" label-position="left" label-width="120px" style="padding: 0 20px">
        <el-alert
            :closable="false"
            title="以下配置可实时预览，开发者可在 config/index.js 中配置默认值，非常不建议在生产环境下开放布局设置"
            type="error"
        ></el-alert>
        <el-divider></el-divider>
        <el-form-item :label="$t('user.theme_mode')">
            <el-select v-model="currentThemeMode" placeholder="请选择主题模式">
                <el-option :label="themeModeLabel('auto', '跟随系统')" value="auto"> </el-option>
                <el-option
                    :label="themeModeLabel('light', '浅色模式')"
                    value="light"
                    :disabled="isCurrentMode('light')"
                >
                </el-option>
                <el-option :label="themeModeLabel('dark', '深色模式')" value="dark" :disabled="isCurrentMode('dark')">
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item :label="$t('user.language')">
            <el-select v-model="appLang">
                <el-option label="简体中文" value="zh-cn"></el-option>
                <el-option label="English" value="en"></el-option>
            </el-select>
        </el-form-item>
        <el-divider></el-divider>
        <el-form-item label="主题颜色">
            <el-color-picker
                v-model="appPrimaryColor"
                :predefine="colorList"
                :disabled="isColorPickerDisabled"
            ></el-color-picker>
        </el-form-item>
        <el-divider></el-divider>
        <el-form-item label="框架布局">
            <el-select v-model="appLayout" placeholder="请选择">
                <el-option label="默认" value="default"></el-option>
                <el-option label="通栏" value="header"></el-option>
                <el-option label="经典" value="menu"></el-option>
                <el-option label="功能坞" value="dock"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="折叠菜单">
            <el-switch v-model="appMenuCollapse" :disabled="appLayout === 'dock'"></el-switch>
        </el-form-item>
        <el-form-item label="标签栏">
            <el-switch v-model="appShowTabs"></el-switch>
        </el-form-item>
        <el-divider></el-divider>
    </el-form>
</template>

<script>
import { useLayoutStore } from '@/stores/layout';
import { useGlobalStore } from '@/stores/global';
import systemConfig from '@/config/index';
import storageConfig from '@/config/storage';
import theme from '@/utils/theme';

export default {
    data() {
        return {
            layoutStore: useLayoutStore(),
            globalStore: useGlobalStore(),
            systemDarkMode: window.matchMedia('(prefers-color-scheme: dark)').matches,
            appLayout: useLayoutStore().appLayout,
            appMenuCollapse: useLayoutStore().appMenuCollapse,
            appShowTabs: useLayoutStore().appShowTabs,
            colorList: systemConfig.COLOR_LIST,
            mediaQuery: null,
            themeChangeHandler: null,
            themeColorHandler: null
        };
    },
    computed: {
        /**
         * 当前语言
         */
        appLang: {
            get() {
                return this.$i18n.locale;
            },
            set(val) {
                this.$i18n.locale = val;
                this.$TOOL.data.set(storageConfig.vars.appLang, val);
            }
        },
        /**
         * 当前主题颜色
         */
        appPrimaryColor: {
            get() {
                return this.layoutStore.appPrimaryColor;
            },
            set(val) {
                this.layoutStore.setAppPrimaryColor(val);
            }
        },
        /**
         * 当前主题模式
         */
        currentThemeMode: {
            get() {
                return this.layoutStore.appThemeMode;
            },
            set(value) {
                this.layoutStore.setAppThemeMode(value);
            }
        },
        /**
         * 是否禁用颜色选择器
         * @returns {boolean|boolean}
         */
        isColorPickerDisabled() {
            const mode = this.layoutStore.appThemeMode;
            return mode === 'dark' || (mode === 'auto' && this.systemDarkMode);
        }
    },
    methods: {
        /**
         * 判断当前模式是否为指定模式
         * @param mode
         * @returns {boolean}
         */
        isCurrentMode(mode) {
            if (this.currentThemeMode !== 'auto') return false;
            return this.systemDarkMode ? mode === 'dark' : mode === 'light';
        },
        /**
         * 获取主题模式显示文本
         * @param mode
         * @param label
         * @returns {string}
         */
        themeModeLabel(mode, label) {
            return theme.getThemeModeLabel(mode, label);
        }
    },
    mounted() {
        this.mediaQuery = theme.mediaQuery;
        this.themeChangeHandler = e => {
            this.systemDarkMode = e.matches;
        };
        this.mediaQuery.addEventListener('change', this.themeChangeHandler);
        this.themeColorHandler = e => {
            this.appPrimaryColor = e.detail.color;
        };
        window.addEventListener('theme-color-change', this.themeColorHandler);
    },
    beforeDestroy() {
        if (this.mediaQuery && this.themeChangeHandler) {
            this.mediaQuery.removeEventListener('change', this.themeChangeHandler);
        }
        if (this.themeColorHandler) {
            window.removeEventListener('theme-color-change', this.themeColorHandler);
        }
    },
    watch: {
        /**
         * 监听语言变化
         * @param val
         */
        appLayout(val) {
            this.layoutStore.setAppLayout(val);
        },
        /**
         * 监听菜单是否折叠
         * @param val
         */
        appMenuCollapse(val) {
            this.layoutStore.setAppMenuCollapse(val);
        },
        /**
         * 监听是否显示标签栏
         * @param val
         */
        appShowTabs(val) {
            this.layoutStore.setAppShowTabs(val);
        },
        /**
         * 监听主题颜色变化
         * @param val
         */
        'layoutStore.appLayout'(val) {
            this.appLayout = val;
        },
        /**
         * 监听菜单是否折叠
         * @param val
         */
        'layoutStore.appMenuCollapse'(val) {
            this.appMenuCollapse = val;
        },
        /**
         * 监听是否显示标签栏
         * @param val
         */
        'layoutStore.appShowTabs'(val) {
            this.appShowTabs = val;
        }
    }
};
</script>

<style></style>
