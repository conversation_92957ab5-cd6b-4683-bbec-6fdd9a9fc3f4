import { lockScreenManager } from '@/utils/lockscreen';
import storageConfig from '@/config/storage';

export default {
    render() {},
    data() {
        return {
            logoutCount: this.$TOOL.data.get(storageConfig.vars.autoLockscreen)
        };
    },
    mounted() {
        if (this.logoutCount) {
            lockScreenManager.setupAutoLock(parseInt(this.logoutCount));
        }
    },
    unmounted() {
        lockScreenManager.clearAutoLock();
    }
};
