import type { ComponentOptions } from 'vue';
import { lockScreenManager } from '@/utils/lockscreen';
import storageConfig from '@/config/storage';

// 组件数据类型
interface AutoLockscreenData {
    logoutCount: string | number | null;
}

// 组件实例类型
interface AutoLockscreenInstance {
    $TOOL: any;
    logoutCount: string | number | null;
}

const autoLockscreenComponent: ComponentOptions = {
    render() {},
    data(): AutoLockscreenData {
        return {
            logoutCount: (this as any).$TOOL.data.get(storageConfig.vars.autoLockscreen)
        };
    },
    mounted(this: AutoLockscreenInstance) {
        if (this.logoutCount) {
            lockScreenManager.setupAutoLock(parseInt(String(this.logoutCount)));
        }
    },
    unmounted() {
        lockScreenManager.clearAutoLock();
    }
};

export default autoLockscreenComponent;
