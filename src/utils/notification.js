import { ElMessage } from 'element-plus';
import notificationApi from '@/api/model/notification';
import { useNotificationStore } from '@/stores/notification';
import { debounce } from '@/utils/throttleDebounce';

/**
 * 搜索消息列表，防抖处理后调用 table 的 upData 方法
 * @param {Object} tableRef - 消息表格组件引用
 * @param {Object} searchParams - 搜索参数
 */
export const searchNotification = debounce((tableRef, searchParams) => {
    if (tableRef && typeof tableRef.upData === 'function') {
        tableRef.upData(searchParams);
    }
}, 300);

export default class NotificationUtils {
    /**
     * 更新未读数
     */
    static async updateUnreadCount() {
        const notificationStore = useNotificationStore();
        await notificationStore.getUnreadCount();
    }

    /**
     * 标记消息状态（添加防抖）
     */
    static markStatus = debounce(async (id, isRead = true) => {
        try {
            const { status } = await notificationApi.status.post({
                id,
                action: isRead ? undefined : 'unread'
            });

            if (status === 1) {
                await this.updateUnreadCount();
                ElMessage.success(isRead ? '标记成功' : '已标记为未读');
                return { success: true };
            }
            return { success: false, message: '操作失败' };
        } catch (error) {
            console.error(`标记${isRead ? '已读' : '未读'}失败:`, error);
            return { success: false, message: '操作失败' };
        }
    }, 300);

    /**
     * 批量标记消息状态（添加防抖）
     * @param {Array} ids - 消息ID数组
     * @param {boolean} [isRead=true] - 是否标记为已读
     */
    static markBatchStatus = debounce(async (ids, isRead = true) => {
        if (!ids?.length) return { success: false, message: '未选择消息' };

        try {
            const { status } = await notificationApi.batchStatus.post({
                ids,
                action: isRead ? undefined : 'unread'
            });
            if (status === 1) {
                await this.updateUnreadCount();
                ElMessage.success(`批量标记${isRead ? '已读' : '未读'}成功`);
                return { success: true };
            }
            return { success: false, message: '操作失败' };
        } catch (error) {
            console.error(`批量标记${isRead ? '已读' : '未读'}失败:`, error);
            return { success: false, message: '操作失败' };
        }
    }, 300);

    /**
     * 标记全部消息为已读（添加防抖）
     */
    static markAllRead = debounce(async () => {
        try {
            const { status } = await notificationApi.markAllRead.post();
            if (status === 1) {
                await this.updateUnreadCount();
                ElMessage.success('全部标记已读成功');
                return { success: true };
            }
            return { success: false, message: '操作失败' };
        } catch (error) {
            console.error('全部标记已读失败:', error);
            return { success: false, message: '操作失败' };
        }
    }, 300);

    /**
     * 消息交互操作（添加防抖）
     */
    static interact = debounce(async (id, action = 'like') => {
        try {
            const { status } = await notificationApi.interact.post({ id, action });
            if (status === 1) {
                ElMessage.success(`${action === 'like' ? '点赞' : '踩'}成功`);
                return { success: true };
            }
            return { success: false, message: '操作失败' };
        } catch (error) {
            console.error('交互操作失败:', error);
            return { success: false, message: '操作失败' };
        }
    }, 300);

    /**
     * 批量删除消息（添加防抖）
     */
    static batchDelete = debounce(async ids => {
        if (!ids?.length) return { success: false, message: '未选择消息' };

        try {
            const { status } = await notificationApi.batchDelete.post({ ids });
            if (status === 1) {
                const notificationStore = useNotificationStore();
                await notificationStore.getUnreadCount();
                ElMessage.success('批量删除成功');
                return { success: true };
            }
            return { success: false, message: '操作失败' };
        } catch (error) {
            console.error('批量删除失败:', error);
            return { success: false, message: '删除失败' };
        }
    }, 300);

    /**
     * 删除单条消息（添加防抖）
     */
    static deleteMessage = debounce(async id => {
        try {
            const { status } = await notificationApi.delete.post({ id });
            if (status === 1) {
                await this.updateUnreadCount();
                ElMessage.success('删除成功');
                return { success: true };
            }
            return { success: false, message: '删除失败' };
        } catch (error) {
            console.error('删除失败:', error);
            return { success: false, message: '删除失败' };
        }
    }, 300);

    /**
     * 搜索消息列表（添加防抖）
     */
    static searchMessages = debounce(async (params = {}, callback) => {
        try {
            const result = await this.loadMessages(params);
            if (typeof callback === 'function') {
                callback(result);
            }
            return result;
        } catch (error) {
            console.error('搜索失败:', error);
            return { success: false, message: '搜索失败' };
        }
    }, 300);

    /**
     * 发送消息（添加防抖）
     */
    static sendMessage = debounce(async data => {
        try {
            const { status } = await notificationApi.send.post(data);
            if (status === 1) {
                ElMessage.success('发送成功');
                return { success: true };
            }
            return { success: false, message: '发送失败' };
        } catch (error) {
            console.error('发送消息失败:', error);
            return { success: false, message: '发送失败' };
        }
    }, 500); // 发送消息使用更长的防抖时间

    /**
     * 加载消息列表（添加防抖）
     */
    static loadMessageList = debounce(async (params = {}) => {
        try {
            return await this.loadMessages(params);
        } catch (error) {
            console.error('加载列表失败:', error);
            return { success: false, message: '加载失败' };
        }
    }, 300);

    // 其他辅助方法保持不变
    static async loadMessages(params = {}) {
        const defaultParams = {
            page: 1,
            pageSize: 20,
            readStatus: 0
        };
        const mergedParams = { ...defaultParams, ...params };
        try {
            const { status, data } = await notificationApi.list.get(mergedParams);
            if (status === 1 && data?.list) {
                return {
                    success: true,
                    data: data.list,
                    total: data.total
                };
            }
            return { success: false, message: '加载失败' };
        } catch (error) {
            console.error('加载消息失败:', error);
            return { success: false, message: '加载失败' };
        }
    }

    static async updateMessageStatus(message, isRead = true, updateCallback = null) {
        const { success } = await this.markStatus(message.id, isRead);
        if (success) {
            // 更新消息状态
            message.is_read = isRead;

            // 更新列表数据
            if (typeof updateCallback === 'function') {
                await updateCallback();
            }
        }
        return success;
    }

    static async handleBatchOperation(rows, type = 'read', updateCallback = null) {
        if (!rows?.length) return false;

        try {
            let success = false;
            if (type === 'read') {
                const unreadIds = rows.filter(row => !row.is_read).map(row => row.id);
                const result = await this.markBatchStatus(unreadIds);
                success = result.success;
            } else if (type === 'delete') {
                const ids = rows.map(row => row.id);
                const result = await this.batchDelete(ids);
                success = result.success;
            }

            if (success) {
                // 更新全局未读数
                const notificationStore = useNotificationStore();
                await notificationStore.getUnreadCount();

                // 更新列表数据
                if (typeof updateCallback === 'function') {
                    await updateCallback();
                }
            }
            return success;
        } catch (error) {
            console.error('批量操作失败:', error);
            return false;
        }
    }

    /**
     * 处理消息互动
     * @param {string} type - 互动类型
     * @param {Object} message - 消息对象
     * @param {Function} updateCallback - 更新回调函数
     */
    static async handleInteraction(type, message, updateCallback) {
        if (!message?.id) return false;

        try {
            // 确定互动操作名称
            let action;
            if (type === 'like') {
                action = message.is_liked ? 'cancel' : 'like';
            } else if (type === 'dislike') {
                action = message.is_disliked ? 'cancel' : 'dislike';
            }

            const { status, message: msg } = await notificationApi.interact.post({
                id: message.id,
                action
            });

            if (status === 1) {
                // 使用新方法获取并更新状态
                await this.getInteractStatus(message.id, message);

                if (typeof updateCallback === 'function') {
                    updateCallback();
                }

                ElMessage.success(msg || '操作成功');
                return true;
            }
            throw new Error(msg || '操作失败');
        } catch (error) {
            console.error('消息互动失败:', error);
            ElMessage.error(error.message || '操作失败');
            return false;
        }
    }

    /**
     * 获取消息互动状态
     * @param {number} messageId - 消息ID
     * @param {Object} message - 消息对象（可选，如果提供则直接更新）
     */
    static async getInteractStatus(messageId, message = null) {
        try {
            const { status, data } = await notificationApi.interactStatus.get(messageId);
            if (status === 1 && data) {
                // 如果提供了消息对象，直接更新其状态
                if (message) {
                    if (!message.counter) message.counter = {};
                    message.counter.like_count = data.like_count;
                    message.counter.dislike_count = data.dislike_count;
                    message.is_liked = data.interact_status === 1;
                    message.is_disliked = data.interact_status === -1;
                }
                return { success: true, data };
            }
            return { success: false, message: '获取互动状态失败' };
        } catch (error) {
            console.error('获取互动状态失败:', error);
            return { success: false, message: error.message || '获取互动状态失败' };
        }
    }
}
