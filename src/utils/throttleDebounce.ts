/**
 * 防抖函数（可以配合指令一起使用v-debounce）
 * @param fn - 需要防抖的函数
 * @param delay - 延迟时间，默认300ms
 * @param immediate - 是否立即执行，默认false
 * @returns 防抖后的函数,返回Promise
 */
export function debounce<T extends (...args: any[]) => any>(
    fn: T, 
    delay: number = 300, 
    immediate: boolean = false
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
    let timer: NodeJS.Timeout | null = null;
    return function (...args: Parameters<T>): Promise<ReturnType<T>> {
        return new Promise(resolve => {
            const context = this;

            if (timer) clearTimeout(timer);

            if (immediate && !timer) {
                Promise.resolve(fn.apply(context, args)).then(resolve);
            } else {
                timer = setTimeout(async () => {
                    const result = await fn.apply(context, args);
                    timer = null;
                    resolve(result);
                }, delay);
            }
        });
    };
}

// 节流配置选项类型
interface ThrottleOptions {
    leading?: boolean;
    trailing?: boolean;
}

/**
 * 节流函数（可以配合指令一起使用v-throttle）
 * @param fn - 需要节流的函数
 * @param delay - 延迟时间，默认300ms
 * @param options - 配置项
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
    fn: T, 
    delay: number = 300, 
    options: ThrottleOptions = {}
): (...args: Parameters<T>) => void {
    let timer: NodeJS.Timeout | null = null;
    let previous = 0;

    const { leading = true, trailing = true } = options;

    return function (...args: Parameters<T>): void {
        const context = this;
        const now = Date.now();

        if (!previous && !leading) {
            previous = now;
        }

        const remaining = delay - (now - previous);

        if (remaining <= 0 || remaining > delay) {
            if (timer) {
                clearTimeout(timer);
                timer = null;
            }

            previous = now;
            fn.apply(context, args);
        } else if (!timer && trailing) {
            timer = setTimeout(() => {
                previous = leading ? Date.now() : 0;
                timer = null;
                fn.apply(context, args);
            }, remaining);
        }
    };
}

// Vue指令用的防抖函数
export const vDebounce = {
    mounted(el: HTMLElement, binding: any) {
        const { value, arg } = binding;
        const delay = arg ? parseInt(arg) : 300;
        
        if (typeof value === 'function') {
            el.addEventListener('click', debounce(value, delay));
        }
    }
};

// Vue指令用的节流函数
export const vThrottle = {
    mounted(el: HTMLElement, binding: any) {
        const { value, arg } = binding;
        const delay = arg ? parseInt(arg) : 300;
        
        if (typeof value === 'function') {
            el.addEventListener('click', throttle(value, delay));
        }
    }
};
