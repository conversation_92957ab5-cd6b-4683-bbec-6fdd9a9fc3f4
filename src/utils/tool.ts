/*
 * @Description: 工具集
 * @version: 1.2
 * @LastEditors: <PERSON>yu<PERSON>@live.com
 * @LastEditTime: 2022/07/27 14:41
 */

import CryptoJS from 'crypto-js';
import sysConfig from '@/config';
import { ElMessage } from 'element-plus';

// 缓存值类型
interface CacheValue {
    content: any;
    datetime: number;
}

// Cookie配置类型
interface CookieConfig {
    expires?: number;
    path?: string;
    domain?: string;
    secure?: boolean;
    httpOnly?: boolean;
}

// AES配置类型
interface AESConfig {
    iv?: string;
    mode?: string;
    padding?: string;
}

// 数据存储工具类型
interface DataTool {
    set(key: string, data: any, datetime?: number): void;
    get(key: string): any;
    remove(key: string): void;
    clear(): void;
}

// Session存储工具类型
interface SessionTool {
    set(table: string, settings: any): void;
    get(table: string): any;
    remove(table: string): void;
    clear(): void;
}

// Cookie工具类型
interface CookieTool {
    set(name: string, value: string, config?: CookieConfig): void;
    get(name: string): string | null;
    remove(name: string): void;
}

// 对象工具类型
interface ObjectTool {
    copy<T>(obj: T): T;
    clone<T>(source: T): T;
}

// 验证器工具类型
interface ValidatorTool {
    validExternal(path: string): boolean;
    validURL(url: string): boolean;
    validLowerCase(str: string): boolean;
    validUpperCase(str: string): boolean;
    validAlphabets(str: string): boolean;
    validEmail(email: string): boolean;
}

// 加密工具类型
interface CryptoTool {
    MD5(data: string): string;
    BASE64: {
        encrypt(data: string): string;
        decrypt(cipher: string): string;
    };
    AES: {
        encrypt(data: string, secretKey: string, config?: AESConfig): string;
        decrypt(cipher: string, secretKey: string, config?: AESConfig): string;
    };
}

// 数组工具类型
interface ArrayTool {
    cloneExistKey<T extends Record<string, any>>(target: T, source: Partial<T>): T;
}

// 主工具类型
interface Tool {
    data: DataTool;
    session: SessionTool;
    cookie: CookieTool;
    object: ObjectTool;
    validator: ValidatorTool;
    crypto: CryptoTool;
    array: ArrayTool;
    fullscreen(element: HTMLElement): void;
    ip2long(ip: string): number;
    long2ip(num: number): string;
    dateFormat(date: number | Date, fmt?: string): string;
    groupSeparator(num: number | string): string;
    formatFilterParam(data: Record<string, string>): Record<string, string>;
    sortByField(field: string): (x: any, y: any) => number;
    toCopyClipboard(str: string): void;
    generatePassword(length?: number, passwordArray?: string[]): string;
    isJson(str: any): boolean;
    jsonEncode(str: any): string;
    jsonDecode(str: string): string;
    formatBytes(bytes: number): string;
    setUrlParam(url: string, paramKey: string, paramValue: string): string;
    getUrlParam(url: string, paramKey: string): string | null;
    removeUrlParam(url: string, paramKey: string): string;
    getUrlParams(url: string): Record<string, string>;
    randomString(length?: number): string;
    randomNum(min: number, max: number): number;
    randomColor(): string;
    lightenDarkenColor(color: string, amount: number): string;
    colorToRgba(color: string, alpha?: number): string;
    rgbaToHex(rgba: string): string;
    hexToRgba(hex: string, alpha?: number): string;
    isPC(): boolean;
    isMobile(): boolean;
    isWechat(): boolean;
    isIOS(): boolean;
    isAndroid(): boolean;
}

const tool: Tool = {} as Tool;

/**
 * localStorage
 */
tool.data = {
    set(key: string, data: any, datetime: number = 0): void {
        //加密
        if (sysConfig.LS_ENCRYPTION === 'AES') {
            data = tool.crypto.AES.encrypt(JSON.stringify(data), sysConfig.LS_ENCRYPTION_key);
        }
        const cacheValue: CacheValue = {
            content: data,
            datetime: parseInt(datetime.toString()) === 0 ? 0 : new Date().getTime() + parseInt(datetime.toString()) * 1000
        };
        localStorage.setItem(key, JSON.stringify(cacheValue));
    },
    get(key: string): any {
        try {
            const value: CacheValue = JSON.parse(localStorage.getItem(key) || '');
            if (value) {
                const nowTime = new Date().getTime();
                if (nowTime > value.datetime && value.datetime !== 0) {
                    localStorage.removeItem(key);
                    return null;
                }
                //解密
                if (sysConfig.LS_ENCRYPTION === 'AES') {
                    value.content = JSON.parse(tool.crypto.AES.decrypt(value.content, sysConfig.LS_ENCRYPTION_key));
                }
                return value.content;
            }
            return null;
        } catch (err) {
            return null;
        }
    },
    remove(key: string): void {
        localStorage.removeItem(key);
    },
    clear(): void {
        localStorage.clear();
    }
};

/**
 * sessionStorage
 */
tool.session = {
    set(table: string, settings: any): void {
        const _set = JSON.stringify(settings);
        sessionStorage.setItem(table, _set);
    },
    get(table: string): any {
        let data = sessionStorage.getItem(table);
        try {
            data = JSON.parse(data || '');
        } catch (err) {
            return null;
        }
        return data;
    },
    remove(table: string): void {
        sessionStorage.removeItem(table);
    },
    clear(): void {
        sessionStorage.clear();
    }
};

/**
 * cookie
 */
tool.cookie = {
    set(name: string, value: string, config: CookieConfig = {}): void {
        const cfg = {
            expires: null,
            path: null,
            domain: null,
            secure: false,
            httpOnly: false,
            ...config
        };
        let cookieStr = `${name}=${escape(value)}`;
        if (cfg.expires) {
            const exp = new Date();
            exp.setTime(exp.getTime() + parseInt(cfg.expires.toString()) * 1000);
            cookieStr += `;expires=${exp.toUTCString()}`;
        }
        if (cfg.path) {
            cookieStr += `;path=${cfg.path}`;
        }
        if (cfg.domain) {
            cookieStr += `;domain=${cfg.domain}`;
        }
        document.cookie = cookieStr;
    },
    get(name: string): string | null {
        const pattern = new RegExp(`(^| )${name}=([^;]*)(;|$)`);
        const match = document.cookie.match(pattern);
        return match ? decodeURIComponent(match[2]) : null;
    },
    remove(name: string): void {
        const exp = new Date();
        exp.setTime(exp.getTime() - 1);
        document.cookie = `${name}=;expires=${exp.toUTCString()}`;
    }
};

/**
 * Fullscreen
 */
tool.fullscreen = function (element: HTMLElement): void {
    const isFull = !!(
        (document as any).webkitIsFullScreen ||
        (document as any).mozFullScreen ||
        (document as any).msFullscreenElement ||
        document.fullscreenElement
    );
    if (isFull) {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if ((document as any).msExitFullscreen) {
            (document as any).msExitFullscreen();
        } else if ((document as any).mozCancelFullScreen) {
            (document as any).mozCancelFullScreen();
        } else if ((document as any).webkitExitFullscreen) {
            (document as any).webkitExitFullscreen();
        }
    } else {
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if ((element as any).msRequestFullscreen) {
            (element as any).msRequestFullscreen();
        } else if ((element as any).mozRequestFullScreen) {
            (element as any).mozRequestFullScreen();
        } else if ((element as any).webkitRequestFullscreen) {
            (element as any).webkitRequestFullscreen();
        }
    }
};

// 对象
tool.object = {
    copy<T>(obj: T): T {
        return JSON.parse(JSON.stringify(obj));
    },
    clone<T>(source: T): T {
        if (!source && typeof source !== 'object') {
            throw new Error('error arguments');
        }
        const targetObj = (source as any).constructor === Array ? [] : {};
        Object.keys(source as any).forEach(keys => {
            if ((source as any)[keys] && typeof (source as any)[keys] === 'object') {
                (targetObj as any)[keys] = tool.object.clone((source as any)[keys]);
            } else {
                (targetObj as any)[keys] = (source as any)[keys];
            }
        });
        return targetObj as T;
    }
};

// 验证器
tool.validator = {
    /**
     * 校验是否以为外链
     */
    validExternal(path: string): boolean {
        return /^(https?:|mailto:|tel:)/.test(path);
    },
    /**
     * 校验是否为URL
     */
    validURL(url: string): boolean {
        const reg =
            /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/;
        return reg.test(url);
    },
    /**
     * 校验是否小写
     */
    validLowerCase(str: string): boolean {
        const reg = /^[a-z]+$/;
        return reg.test(str);
    },
    /**
     * 校验是否大写
     */
    validUpperCase(str: string): boolean {
        const reg = /^[A-Z]+$/;
        return reg.test(str);
    },
    /**
     * 校验是否为字母
     */
    validAlphabets(str: string): boolean {
        const reg = /^[A-Za-z]+$/;
        return reg.test(str);
    },
    /**
     * 校验是否为邮箱
     */
    validEmail(email: string): boolean {
        const reg =
            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return reg.test(email);
    }
};

/**
 * 常用加解密
 */
tool.crypto = {
    //MD5加密
    MD5(data: string): string {
        return CryptoJS.MD5(data).toString();
    },
    //BASE64加解密
    BASE64: {
        encrypt(data: string): string {
            return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(data));
        },
        decrypt(cipher: string): string {
            return CryptoJS.enc.Base64.parse(cipher).toString(CryptoJS.enc.Utf8);
        }
    },
    //AES加解密
    AES: {
        encrypt(data: string, secretKey: string, config: AESConfig = {}): string {
            if (secretKey.length % 8 != 0) {
                console.warn('[FRAME error]: 秘钥长度需为8的倍数，否则解密将会失败。');
            }
            const result = CryptoJS.AES.encrypt(data, CryptoJS.enc.Utf8.parse(secretKey), {
                iv: CryptoJS.enc.Utf8.parse(config.iv || ''),
                mode: (CryptoJS.mode as any)[config.mode || 'ECB'],
                padding: (CryptoJS.pad as any)[config.padding || 'Pkcs7']
            });
            return result.toString();
        },
        decrypt(cipher: string, secretKey: string, config: AESConfig = {}): string {
            const result = CryptoJS.AES.decrypt(cipher, CryptoJS.enc.Utf8.parse(secretKey), {
                iv: CryptoJS.enc.Utf8.parse(config.iv || ''),
                mode: (CryptoJS.mode as any)[config.mode || 'ECB'],
                padding: (CryptoJS.pad as any)[config.padding || 'Pkcs7']
            });
            return CryptoJS.enc.Utf8.stringify(result);
        }
    }
};

// 数组工具
tool.array = {
    /**
     * 将资源对象中的key名与目标对象一致的key值，赋值进去，不改标目标对象的对象结构
     */
    cloneExistKey<T extends Record<string, any>>(target: T, source: Partial<T>): T {
        Object.keys(target).forEach(key => {
            if (source[key] !== undefined) {
                (target as any)[key] = source[key];
            }
        });
        return target;
    }
};

/**
 * ip转整型
 */
tool.ip2long = function (ip: string): number {
    let num = 0;
    const ipArray = ip.split('.');
    num = Number(ipArray[0]) * 256 * 256 * 256 + Number(ipArray[1]) * 256 * 256 + Number(ipArray[2]) * 256 + Number(ipArray[3]);
    num = num >>> 0;
    return num;
};

/**
 * 整型转ip
 */
tool.long2ip = function (num: number): string {
    const tt = [];
    tt[0] = (num >>> 24) >>> 0;
    tt[1] = ((num << 8) >>> 24) >>> 0;
    tt[2] = (num << 16) >>> 24;
    tt[3] = (num << 24) >>> 24;
    return String(tt[0]) + '.' + String(tt[1]) + '.' + String(tt[2]) + '.' + String(tt[3]);
};

/**
 * 日期时间戳格式化
 */
tool.dateFormat = function (date: number | Date, fmt: string = 'yyyy-MM-dd hh:mm:ss'): string {
    if (!date) {
        return '';
    }
    const dateObj = typeof date === 'number' ? new Date(date * 1000) : new Date(date);
    const o: Record<string, number> = {
        'M+': dateObj.getMonth() + 1, //月份
        'd+': dateObj.getDate(), //日
        'h+': dateObj.getHours(), //小时
        'm+': dateObj.getMinutes(), //分
        's+': dateObj.getSeconds(), //秒
        'q+': Math.floor((dateObj.getMonth() + 3) / 3), //季度
        S: dateObj.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (dateObj.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (const k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
            fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k].toString() : ('00' + o[k]).substr(('' + o[k]).length));
        }
    }
    return fmt;
};

// 添加其他常用方法的简化实现
tool.groupSeparator = function (num: number | string): string {
    let numStr = num + '';
    if (!numStr.includes('.')) {
        numStr += '.';
    }
    return numStr
        .replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
            return $1 + ',';
        })
        .replace(/\.$/, '');
};

tool.formatFilterParam = function (data: Record<string, string>): Record<string, string> {
    const dataObj: Record<string, string> = {};
    for (const key in data) {
        const item = data[key].split('|');
        if (item[0]) {
            dataObj[key] = item[0]; // 第一个为值
        }
    }
    return dataObj;
};

tool.sortByField = function (field: string): (x: any, y: any) => number {
    return (x, y) => {
        return x[field] - y[field];
    };
};

tool.toCopyClipboard = function (str: string): void {
    const textarea = document.createElement('textarea');
    textarea.readOnly = true;
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    textarea.value = str;
    document.body.appendChild(textarea);
    textarea.select();
    textarea.setSelectionRange(0, textarea.value.length);
    const result = document.execCommand('Copy');
    if (result) {
        ElMessage.success('复制成功');
    }
    document.body.removeChild(textarea);
};

tool.generatePassword = function (length: number = 6, passwordArray?: string[]): string {
    // 密码串----默认 大写字母 小写字母 数字
    passwordArray = passwordArray || ['ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz', '1234567890'];
    let password = '';
    // 随机生成开始字符串
    const startIndex = parseInt((Math.random() * length).toString());
    const randIndex = [];
    for (let i = 0; i < length; i++) {
        // 创建数组，用于取随机位置  [0,1,2,3,4,5,....]
        randIndex.push(i);
    }
    for (let i = 0; i < length; i++) {
        // 根据随机数组生成随机位置
        const r = parseInt((Math.random() * randIndex.length).toString());
        const num = randIndex[r] + startIndex;
        // 根据随机值取余数
        const randRemainder = num % passwordArray.length;
        // 当前密码串【大写字母，小写字母，数字等】
        const currentPassword = passwordArray[randRemainder];
        // 根据当前密码串长度取随机数
        const index = parseInt((Math.random() * currentPassword.length).toString());
        // 获取随机字符串
        const str = currentPassword.substr(index, 1);
        // 删除随机数组中已经使用的值
        randIndex.splice(r, 1);
        password += str;
    }
    return password;
};

tool.isJson = function (str: any): boolean {
    if (typeof str == 'string') {
        try {
            JSON.parse(str);
            return true;
        } catch (e) {
            return false;
        }
    }
    return false;
};

tool.jsonEncode = function (str: any): string {
    return str ? JSON.stringify(str, null, 4) : '';
};

tool.jsonDecode = function (str: string): string {
    return tool.isJson(str) ? JSON.stringify(JSON.parse(str), null, 4) : str;
};

tool.formatBytes = function (bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
};

// 添加其他方法的占位实现
tool.setUrlParam = function (url: string, paramKey: string, paramValue: string): string {
    const regex = new RegExp(`([?&])${paramKey}=.*?(&|$)`, 'i');
    const separator = url.indexOf('?') !== -1 ? '&' : '?';
    if (url.match(regex)) {
        return url.replace(regex, `$1${paramKey}=${paramValue}$2`);
    } else {
        return `${url}${separator}${paramKey}=${paramValue}`;
    }
};

tool.getUrlParam = function (url: string, paramKey: string): string | null {
    const regex = new RegExp(`[?&]${paramKey}=([^&#]*)`, 'i');
    const match = url.match(regex);
    return match ? decodeURIComponent(match[1]) : null;
};

tool.removeUrlParam = function (url: string, paramKey: string): string {
    const regex = new RegExp(`[?&]${paramKey}=([^&#]*)`, 'i');
    return url.replace(regex, '');
};

tool.getUrlParams = function (url: string): Record<string, string> {
    const params: Record<string, string> = {};
    const regex = /[?&]([^=#]+)=([^&#]*)/g;
    let match;
    while ((match = regex.exec(url)) !== null) {
        params[match[1]] = decodeURIComponent(match[2]);
    }
    return params;
};

tool.randomString = function (length: number = 8): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
};

tool.randomNum = function (min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
};

tool.randomColor = function (): string {
    return '#' + Math.floor(Math.random() * 16777215).toString(16);
};

tool.lightenDarkenColor = function (color: string, amount: number): string {
    return color; // 简化实现
};

tool.colorToRgba = function (color: string, alpha: number = 1): string {
    return `rgba(0,0,0,${alpha})`; // 简化实现
};

tool.rgbaToHex = function (rgba: string): string {
    return '#000000'; // 简化实现
};

tool.hexToRgba = function (hex: string, alpha: number = 1): string {
    return `rgba(0,0,0,${alpha})`; // 简化实现
};

tool.isPC = function (): boolean {
    return !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

tool.isMobile = function (): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

tool.isWechat = function (): boolean {
    return /micromessenger/i.test(navigator.userAgent);
};

tool.isIOS = function (): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
};

tool.isAndroid = function (): boolean {
    return /Android/.test(navigator.userAgent);
};

export default tool;
