import { createRequest, createJsonp } from './methods';
import { requestQueue } from './queue';
import type { HttpClient } from './types';

// 创建HTTP客户端
const http: HttpClient = {
    get: createRequest('get'),
    post: createRequest('post'),
    put: createRequest('put'),
    patch: createRequest('patch'),
    delete: createRequest('delete'),
    jsonp: createJsonp
};

// 导出请求队列控制方法
export const retryLockscreenRequests = (): void => requestQueue.flush();

// 导出类型
export type { 
    HttpClient, 
    RequestConfig, 
    ResponseData, 
    ApiResponse, 
    RequestFunction,
    HttpMethod,
    RequestError
} from './types';

export default http;
