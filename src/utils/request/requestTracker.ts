// 请求信息类型
export interface RequestInfo {
    timestamp: number;
    url: string;
}

class RequestTracker {
    private currentRequestId: string | null;
    private currentSequenceNum: number;
    private requestHistory: Map<string, RequestInfo>; // 存储最近的请求记录

    constructor() {
        this.currentRequestId = null;
        this.currentSequenceNum = 0;
        this.requestHistory = new Map(); // 存储最近的请求记录
    }

    setRequestId(requestId: string, url: string): void {
        this.currentRequestId = requestId;
        this.requestHistory.set(requestId, {
            timestamp: Date.now(),
            url
        });

        console.log('requestId:::', requestId, 'url:::', url);
        console.log('this.requestHistory:::', this.requestHistory);

        // 只保留最近的 10 条记录
        if (this.requestHistory.size > 10) {
            const oldestKey = this.requestHistory.keys().next().value;
            if (oldestKey) {
                this.requestHistory.delete(oldestKey);
            }
        }
    }

    setSequenceNum(sequenceNum: number): void {
        this.currentSequenceNum = sequenceNum;
    }

    getCurrentSequenceNum(): number {
        return this.currentSequenceNum;
    }

    getCurrentRequestId(): string | null {
        return this.currentRequestId;
    }

    getRequestInfo(requestId: string): RequestInfo | undefined {
        return this.requestHistory.get(requestId);
    }

    // 获取所有请求历史
    getAllRequestHistory(): Map<string, RequestInfo> {
        return new Map(this.requestHistory);
    }

    // 清空请求历史
    clearHistory(): void {
        this.requestHistory.clear();
    }

    // 获取历史记录数量
    getHistorySize(): number {
        return this.requestHistory.size;
    }
}

export const requestTracker = new RequestTracker();
