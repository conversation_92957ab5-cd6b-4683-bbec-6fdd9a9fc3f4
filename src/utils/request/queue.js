import type { RequestConfig } from './types';

// 请求回调函数类型
export type RequestCallback = () => void;

// 队列请求配置类型（确保有url）
export interface QueueRequestConfig extends RequestConfig {
    url: string;
}

class RequestQueue {
    private queue: Map<string, RequestCallback>;

    constructor() {
        this.queue = new Map(); // 使用 Map 存储请求，key 为请求的唯一标识
    }

    // 生成请求的唯一标识
    generateRequestKey(config: QueueRequestConfig): string {
        const { url, params = {}, data = {} } = config;
        // 将 URL、请求参数和请求体序列化后组合成唯一标识
        return `${url}|${JSON.stringify(params)}|${JSON.stringify(data)}`;
    }

    // 添加请求到队列，同一个请求只保留最新的
    enqueue(callback: RequestCallback, config: QueueRequestConfig): void {
        const requestKey = this.generateRequestKey(config);
        this.queue.set(requestKey, callback);
    }

    // 执行并清空队列
    flush(): void {
        // 只执行每个唯一请求的最新回调
        for (const callback of this.queue.values()) {
            callback();
        }
        this.clear();
    }

    // 清空队列
    clear(): void {
        this.queue.clear();
    }

    // 获取队列大小
    size(): number {
        return this.queue.size;
    }
}

export const requestQueue = new RequestQueue();
