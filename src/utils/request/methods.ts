import service from './service';
import { useGlobalStore } from '@/stores/global';
import type { HttpMethod, RequestConfig, ResponseData, RequestFunction, JsonpCallback } from './types';

/**
 * 创建请求方法
 * @param method HTTP方法
 * @returns 请求函数
 */
export function createRequest(method: HttpMethod): RequestFunction {
    return <T = any>(url: string, data: any = {}, config: RequestConfig = {}): Promise<ResponseData<T>> => {
        return new Promise((resolve, reject) => {
            const globalStore = useGlobalStore();
            const finalUrl = config.external ? url : globalStore.apiDomain + url;
            
            const requestConfig: RequestConfig = {
                method,
                url: finalUrl,
                ...(method === 'get' ? { params: data } : { data }),
                ...config
            };

            service(requestConfig)
                .then(response => resolve(response.data))
                .catch(error => reject(error));
        });
    };
}

/**
 * 创建JSONP请求
 * @param url 请求URL
 * @param name 回调函数名
 * @returns Promise
 */
export function createJsonp(url: string, name: string = 'jsonp'): Promise<any> {
    return new Promise(resolve => {
        const script = document.createElement('script');
        const _id = `jsonp${Math.ceil(Math.random() * 1000000)}`;
        script.id = _id;
        script.type = 'text/javascript';
        script.src = url;

        // 创建全局回调函数
        const callback: JsonpCallback = (response: any) => {
            resolve(response);
            // 清理DOM元素
            const scriptElement = document.getElementById(_id);
            if (scriptElement && scriptElement.parentNode) {
                scriptElement.parentNode.removeChild(scriptElement);
            }
            // 清理全局回调函数
            try {
                delete (window as any)[name];
            } catch (e) {
                (window as any)[name] = undefined;
            }
        };

        // 设置全局回调函数
        (window as any)[name] = callback;

        // 添加到DOM
        const head = document.getElementsByTagName('head')[0];
        if (head) {
            head.appendChild(script);
        }
    });
}
