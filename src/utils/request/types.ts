/**
 * 请求相关类型定义
 */

import type { AxiosRequestConfig, AxiosResponse } from 'axios';

// HTTP方法类型
export type HttpMethod = 'get' | 'post' | 'put' | 'patch' | 'delete';

// 请求配置类型
export interface RequestConfig extends AxiosRequestConfig {
    // 是否为外部请求（不添加API域名前缀）
    external?: boolean;
    // 是否静默处理错误
    silent?: boolean;
    // 是否显示加载状态
    loading?: boolean;
    // 是否显示错误提示
    showError?: boolean;
    // 是否缓存请求
    cache?: boolean;
    // 缓存时间（秒）
    cacheTime?: number;
    // 是否重试
    retry?: boolean;
    // 重试次数
    retryCount?: number;
    // 重试延迟（毫秒）
    retryDelay?: number;
    // 是否在锁屏时排队
    queueOnLockscreen?: boolean;
}

// 响应数据类型
export interface ResponseData<T = any> {
    code: number;
    message: string;
    data: T;
    success: boolean;
    timestamp?: number;
    requestId?: string;
    label?: string; // 添加label字段用于特殊状态处理
}

// API响应类型
export interface ApiResponse<T = any> extends AxiosResponse {
    data: ResponseData<T>;
}

// 请求函数类型
export type RequestFunction = <T = any>(
    url: string,
    data?: any,
    config?: RequestConfig
) => Promise<ResponseData<T>>;

// HTTP客户端接口
export interface HttpClient {
    get: RequestFunction;
    post: RequestFunction;
    put: RequestFunction;
    patch: RequestFunction;
    delete: RequestFunction;
    jsonp: (url: string, name?: string) => Promise<any>;
}

// JSONP回调函数类型
export interface JsonpCallback {
    (response: any): void;
}

// 请求队列项类型
export interface QueueItem {
    config: RequestConfig;
    resolve: (value: any) => void;
    reject: (reason: any) => void;
}

// 请求拦截器配置类型
export interface InterceptorConfig {
    // 请求拦截器
    request?: {
        onFulfilled?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>;
        onRejected?: (error: any) => any;
    };
    // 响应拦截器
    response?: {
        onFulfilled?: (response: ApiResponse) => ApiResponse | Promise<ApiResponse>;
        onRejected?: (error: any) => any;
    };
}

// 请求追踪器配置类型
export interface TrackerConfig {
    enabled: boolean;
    maxRequests: number;
    timeout: number;
}

// 错误类型
export interface RequestError extends Error {
    config?: RequestConfig;
    code?: string | number;
    request?: any;
    response?: ApiResponse;
    isAxiosError?: boolean;
}

// 扩展Window类型以支持JSONP
declare global {
    interface Window {
        [key: string]: any;
    }
}
