import { useLockscreenStore } from '@/stores/lockscreen';
import storageConfig from '@/config/storage';
import { ElMessage } from 'element-plus';
import account from '@/api/model/account';
import tool from '@/utils/tool';
import { retryLockscreenRequests } from '@/utils/request';

class LockScreenManager {
    static instance = null;
    lockscreenStore = useLockscreenStore();
    autoLockTimer = null;
    lastActivityTime = Date.now();
    activityEvents = ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart'];
    activityListener = null;

    static getInstance() {
        if (!LockScreenManager.instance) {
            LockScreenManager.instance = new LockScreenManager();
        }
        return LockScreenManager.instance;
    }

    // 检查并同步锁屏状态
    checkAndSyncLockStatus() {
        const cookieLockStatus = parseInt(tool.cookie.get(storageConfig.vars.lockscreen));
        // 同步状态到store
        if (cookieLockStatus === 1 && !this.lockscreenStore.isLocked) {
            this.lockscreenStore.setLockStatus(true);
        }
        // 如果需要锁屏但还未锁定,则进行锁定
        if (this.isScreenLocked()) {
            this.lock();
        }
        return this.isScreenLocked();
    }

    // 统一获取锁屏状态
    isScreenLocked() {
        return this.lockscreenStore.isLocked;
    }

    // 锁定屏幕
    async lock() {
        try {
            const res = await account.lockscreen.post({ type: 1 });
            if (res.status === 1) {
                this.lockscreenStore.setLockStatus(true);
                ElMessage.success('锁定成功');
                return true;
            }
            ElMessage.error(res.message || '锁定失败');
            return false;
        } catch (error) {
            console.error('锁屏失败:', error);
            ElMessage.error('锁屏失败');
            return false;
        }
    }

    // 解锁屏幕
    async unlock(password) {
        try {
            console.log('lockScreenManager.unlock called with password:', password);

            if (!password) {
                ElMessage.error('请输入密码');
                return false;
            }

            const hashedPassword = tool.crypto.MD5(password);
            console.log('发送解锁请求, hashed password:', hashedPassword);

            try {
                console.log('准备发送API请求...');
                const res = await account.unLockscreen.post({
                    password: hashedPassword
                });
                console.log('收到API响应:', res);

                if (!res) {
                    throw new Error('服务器未返回响应');
                }

                if (res.status === 1) {
                    await this.clearLockStatus();
                    ElMessage.success('解锁成功');
                    return true;
                }

                ElMessage.error(res.message || '解锁失败');
                return false;
            } catch (apiError) {
                console.error('API请求失败:', apiError);
                throw apiError;
            }
        } catch (error) {
            console.error('解锁过程出错:', error);
            ElMessage.error(error.message || '解锁失败');
            return false;
        }
    }

    // 清除锁屏状态
    async clearLockStatus() {
        this.lockscreenStore.setLockStatus(false);
        // 重试锁屏期间失败的请求
        await retryLockscreenRequests();
    }

    // 开始监听用户活动
    startActivityMonitoring() {
        this.lastActivityTime = Date.now();
        this.activityListener = () => {
            this.lastActivityTime = Date.now();
        };

        this.activityEvents.forEach(event => {
            document.addEventListener(event, this.activityListener);
        });
    }

    // 停止监听用户活动
    stopActivityMonitoring() {
        if (this.activityListener) {
            this.activityEvents.forEach(event => {
                document.removeEventListener(event, this.activityListener);
            });
            this.activityListener = null;
        }
    }

    // 设置自动锁屏
    setupAutoLock(minutes) {
        this.clearAutoLock();
        this.lockscreenStore.setAutoLockTime(minutes);

        if (minutes > 0) {
            // 开始监听用户活动
            this.startActivityMonitoring();

            // 设置定时器检查用户不活动时间
            this.autoLockTimer = setInterval(() => {
                const now = Date.now();
                const inactiveTime = (now - this.lastActivityTime) / 1000 / 60; // 转换为分钟

                if (inactiveTime >= minutes && !this.isScreenLocked()) {
                    this.lock();
                }
            }, 10000); // 每10秒检查一次
        }
    }

    // 清除自动锁屏
    clearAutoLock() {
        if (this.autoLockTimer) {
            clearInterval(this.autoLockTimer);
            this.autoLockTimer = null;
        }
        this.stopActivityMonitoring();
    }
}

export const lockScreenManager = LockScreenManager.getInstance();
