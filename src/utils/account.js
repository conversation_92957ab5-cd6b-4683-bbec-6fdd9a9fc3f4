/**
 * 系统用户常用函数封装
 */
import { useGlobalStore } from '@/stores/global';
import { useKeepAliveStore } from '@/stores/keepAlive';
import { useIframeStore } from '@/stores/iframe';
import { useViewTabsStore } from '@/stores/viewTabs';
import { permission } from '@/utils/permission';
import { ElMessage, ElMessageBox } from 'element-plus';
import account from '@/api/model/account';
import router from '@/router';
import storageConfig from '@/config/storage';
import tool from '@/utils/tool';
import { usePingStore } from '@/stores/ping';
import { useLockscreenStore } from '@/stores/lockscreen';

export default {
    /**
     * 验证是否为邮箱
     * @returns {*}
     */
    isEmail(str) {
        return /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(str);
    },
    /**
     * 验证是否为手机号
     * @returns {*}
     */
    isMobile(number) {
        const reg = /^1[3456789]\d{9}$/;
        if (number === '' || number === undefined || number == null || number.length === 0) {
            return false;
        } else return reg.test(number);
    },
    /**
     * 任意类型登录后置操作封装
     * @param res
     * @param redirect 是否重定向
     * @returns {Promise<boolean>}
     */
    async loginSuccess(res, redirect) {
        redirect = redirect || false;

        // 用户token放入cookie
        tool.cookie.set(storageConfig.vars.accessToken, res.data.access_token, {
            //expires: res.data.expires_in
            expires: 365 * 24 * 60 * 60
        });

        if (res.data.refresh_token) {
            // 用户refresh_token放入本地缓存
            tool.data.set(storageConfig.vars.refreshToken, res.data.refresh_token);
        }

        if (res.data.user_info) {
            // 用户信息放入本地缓存
            this.userInfo.set(res.data.user_info);
        }

        if (res.data.menu_data) {
            // 菜单数据放入本地缓存
            tool.data.set(storageConfig.vars.userMenu, res.data.menu_data || []);
        }

        if (res.data.permission_data) {
            // 权限节点的别名数据集放入本地缓存
            tool.data.set(storageConfig.vars.userPermission, res.data.permission_data || []);
        }

        const globalStore = useGlobalStore();

        // 用户token标记为非过期，也用于关闭登录框
        globalStore.SET_userTokenExpired(false);

        // 登录对话框标记为已关闭
        globalStore.SET_userLoginDialogVisible(false);

        // 启动心跳
        this.startHeartBeat();

        // 如果是登录对话框模式，就关闭当前对话框，否则跳转到首页
        if (redirect) {
            await router.replace({
                path: '/dashboard'
            });
        }

        return true;
    },
    /**
     * 清除用户本地缓存
     */
    async clearUserCaches() {
        // 停止心跳检测
        this.stopHeartBeat();

        tool.cookie.remove(storageConfig.vars.accessToken);
        tool.cookie.remove(storageConfig.vars.lockscreen);

        tool.data.clear();

        const globalStore = useGlobalStore();
        const keepAliveStore = useKeepAliveStore();
        const iframeStore = useIframeStore();
        const viewTabsStore = useViewTabsStore();
        const lockscreenStore = useLockscreenStore();

        viewTabsStore.clearViewTabs();
        keepAliveStore.clearKeepLive();
        iframeStore.clearIframeList();

        globalStore.SET_userTokenExpired(false);
        globalStore.SET_userLoginDialogVisible(false);

        // 清除锁屏状态使用新的store
        lockscreenStore.setLockStatus(false);

        await router.replace({ path: '/login' });
    },
    /**
     * 开始心跳检测
     */
    startHeartBeat() {
        const pingStore = usePingStore();
        // 避免重复启动
        if (!pingStore.isRunning) {
            pingStore.startPing();
        }
    },
    /**
     * 停止心跳检测
     */
    stopHeartBeat() {
        const pingStore = usePingStore();
        pingStore.stopPing();
    },
    /**
     * 刷新token方法
     * @returns {Promise<boolean>}
     */
    async refreshToken() {
        // 获取本地缓存中的refresh_token
        const _refresh_token = tool.data.get(storageConfig.vars.refreshToken);

        if (_refresh_token) {
            const res = await account.refreshToken.post({ refresh_token: _refresh_token });

            if (res.status === 1) {
                // 任意类型登录后置操作封装
                await this.loginSuccess(res, false);

                return true;
            } else {
                ElMessage.warning(res.message);
                return false;
            }
        }

        ElMessage.warning('刷新token时缺少refresh_token');
        return false;
    },
    /**
     * 用户信息本地缓存处理
     */
    userInfo: {
        // 获取用户信息缓存
        get() {
            let _info = tool.data.get(storageConfig.vars.userInfo);
            if (_info && _info.gender) {
                // 性别转换为
                _info.gender = _info.gender.toString();
            }
            return _info;
        },
        // 设置用户信息缓存
        set(info) {
            let _data = this.get() || {};

            // 空对象时不设置
            if (Object.keys(info).length === 0 || typeof info != 'object') {
                return _data;
            }

            _data = Object.assign({}, _data, info);

            tool.data.set(storageConfig.vars.userInfo, _data);

            return this.get();
        },
        // 删除用户信息缓存
        remove() {
            return tool.data.remove(storageConfig.vars.userInfo);
        }
    },
    /**
     * 退出登录系统方式
     */
    async logoutSystem(reload) {
        ElMessageBox.confirm('确认是否退出当前用户？', '提示', {
            type: 'warning',
            confirmButtonText: '退出',
            confirmButtonClass: 'el-button--danger'
        })
            .then(async () => {
                const res = await account.logout.post();

                ElMessage.success(res.message);

                // 清除本地缓存
                await this.clearUserCaches();

                if (reload) {
                    window.location.reload();
                }
            })
            .catch(() => {
                // 取消退出
            });
    },
    /**
     * 根据性别获取默认头像信息
     * @param gender
     */
    getDefaultAvatarInfo(gender) {
        const _gender = parseInt(gender) || 2;
        return {
            // 头像地址
            url: _gender === 0 ? 'img/avatar-w.svg' : _gender === 1 ? 'img/avatar-m.svg' : 'img/avatar-a.svg',
            // 性别class
            class: _gender === 0 ? 'woman' : _gender === 1 ? 'man' : 'unknown'
        };
    },
    /**
     * 用户权限集检测封装（data支持数组，且可以不用v-auth，在其它地方判断）
     * @param data
     */
    checkPermission(data) {
        let _has = false;
        if (Array.isArray(data)) {
            data.forEach(item => {
                if (permission(item)) {
                    _has = true;
                }
            });
        } else {
            _has = permission(data);
        }
        return _has;
    },
    /**
     * 检查锁屏状态
     */
    checkLockscreen() {
        return useLockscreenStore().isLocked;
    }
};
