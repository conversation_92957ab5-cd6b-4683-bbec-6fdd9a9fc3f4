import { nextTick } from 'vue';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import router from '@/router';
import { useKeepAliveStore } from '@/stores/keepAlive';
import { useIframeStore } from '@/stores/iframe';
import { useViewTabsStore } from '@/stores/viewTabs';

const keepAliveStore = useKeepAliveStore();
const iframeStore = useIframeStore();
const viewTabsStore = useViewTabsStore();

export default {
    //刷新标签
    refresh() {
        NProgress.start();
        const route = router.currentRoute.value;
        keepAliveStore.removeKeepLive(route.name);
        keepAliveStore.setRouteShow(false);
        nextTick(() => {
            keepAliveStore.pushKeepLive(route.name);
            keepAliveStore.setRouteShow(true);
            NProgress.done();
        });
    },
    //关闭标签
    close(tab) {
        const route = tab || router.currentRoute.value;

        viewTabsStore.removeViewTabs(route);
        iframeStore.removeIframeList(route);
        keepAliveStore.removeKeepLive(route.name);

        const tabList = viewTabsStore.viewTabs;
        const latestView = tabList.slice(-1)[0];

        if (latestView) {
            router.push(latestView);
        } else {
            router.push('/');
        }
    },
    //关闭标签后处理
    closeNext(next) {
        const route = router.currentRoute.value;

        viewTabsStore.removeViewTabs(route);
        iframeStore.removeIframeList(route);
        keepAliveStore.removeKeepLive(route.name);

        if (next) {
            const tabList = viewTabsStore.viewTabs;
            next(tabList);
        }
    },
    //关闭其他
    closeOther() {
        const route = router.currentRoute.value;
        const tabList = [...viewTabsStore.viewTabs];

        tabList.forEach(tab => {
            if ((tab.meta && tab.meta.affix) || route.fullPath == tab.fullPath) {
                return true;
            } else {
                this.close(tab);
            }
        });
    },
    //设置标题
    setTitle(title) {
        viewTabsStore.updateViewTabsTitle(title);
    }
};
