import FingerprintJS from '@fingerprintjs/fingerprintjs';

class Fingerprint {
    constructor() {
        this.fpCache = null;
        this.fpPromise = null;
    }

    async getStableFingerprint() {
        // 如果已有缓存，直接返回
        if (this.fpCache) {
            return this.fpCache;
        }

        // 如果正在获取中，返回正在进行的Promise
        if (this.fpPromise) {
            return this.fpPromise;
        }

        // 创建新的获取指纹Promise
        this.fpPromise = new Promise(async (resolve, reject) => {
            try {
                const fp = await FingerprintJS.load();
                const result = await fp.get();
                this.fpCache = result.visitorId;
                this.fpPromise = null;
                resolve(this.fpCache);
            } catch (error) {
                this.fpPromise = null;
                reject(error);
            }
        });

        return this.fpPromise;
    }

    // 清除缓存（需要时可调用）
    clearCache() {
        this.fpCache = null;
        this.fpPromise = null;
    }
}

export default new Fingerprint();
