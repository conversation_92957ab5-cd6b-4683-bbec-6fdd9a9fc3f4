// 样式变量
// <AUTHOR> <<EMAIL>>
// @Datetime 2024-12-13
// color palette from <https://github.com/vuejs/theme>

:root {
    // 基础变量
    --color-background: #ffffff;
    --color-text: #555;

    // 浅色模式rgb色
    --light-color-rgb-red: 255;
    --light-color-rgb-green: 255;
    --light-color-rgb-blue: 255;

    // 深色模式rgb色
    --dark-color-rgb-red: 0;
    --dark-color-rgb-green: 0;
    --dark-color-rgb-blue: 0;

    // 默认浅色模式下反色rgb色调
    --inverse-color-rgb-red: var(--dark-color-rgb-red);
    --inverse-color-rgb-green: var(--dark-color-rgb-green);
    --inverse-color-rgb-blue: var(--dark-color-rgb-blue);

    // 过渡动画
    --transition-base: all 0.3s;

    // 顶部菜单相关
    --main-header-height: 60px;
    --main-header-padding: 0 20px;
    --main-header-item-radius: 20px;
    --main-header-item-padding: 5px 10px;
    --main-header-item-hover-bg: rgba(0, 0, 0, 0.2);

    // 侧边栏相关
    --main-sidebar-width: 86px;
}

// 系统级暗色主题
@media (prefers-color-scheme: dark) {
}
