// 全局scss函数库
// <AUTHOR> <<EMAIL>>
// @Datetime 2024-12-13

@use 'sass:color';
@use 'sass:math';

// Hex to RGB 转换函数
@function hexToRgb($hex) {
    @return color.red($hex), color.green($hex), color.blue($hex);
}

// Hex to RGBA 转换函数
@function hexToRgba($hex, $alpha: 1) {
    @if type-of($hex) != 'color' {
        $hex: #000000;
    }

    $red: color.red($hex);
    $green: color.green($hex);
    $blue: color.blue($hex);

    @return rgba($red, $green, $blue, $alpha);
}
