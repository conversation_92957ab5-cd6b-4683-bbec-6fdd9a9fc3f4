// 主布局header样式
// <AUTHOR> <<EMAIL>>
// @Datetime 2024-12-13

@use '../global/mixins' as *;

/* 头部 */
.layout-header {
    @include flex-between;
    height: var(--main-header-height);
    background: var(--el-color-primary);
    color: var(--el-color-white) !important;
    padding: var(--main-header-padding);

    .logo-bar {
        @include flex-center;
        font-size: 20px;
        font-weight: bold;

        .logo {
            width: 45px;
            height: 45px;
        }
    }

    .nav {
        display: flex;
        height: 100%;
        margin-left: 40px;

        li {
            @include flex-center;
            padding: 0 10px;
            margin: 0 10px 0 0;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            list-style: none;
            height: 100%;
            cursor: pointer;
            color: #ffffff;

            i {
                margin-right: 5px;
            }
        }
    }

    .user-bar {
        .panel-item {
            border-radius: var(--main-header-item-radius);
            padding: var(--main-header-item-padding);

            &:hover {
                background: var(--main-header-item-hover-bg);
            }
        }

        .user {
            label {
                color: #ffffff;
            }
        }
    }

    .layout-header-left,
    .layout-header-right {
        @include flex-center;
    }
}

.layout-topbar {
    @include flex-between;
    height: var(--main-header-height);
    padding: var(--main-header-padding);
    font-size: 14px;

    .left-panel,
    .right-panel {
        @include flex-center;
    }
}
