// 主布局tabs样式
// <AUTHOR> <<PERSON>yu<PERSON>@live.com>
// @Datetime 2024-12-13

@use '../global/mixins' as *;

/**
 * 主选项卡
 */
.layout-tabs {
    background-color: var(--el-fill-color-light);
    border-bottom: 1px solid var(--el-border-color-dark);
    height: 40px;

    ul {
        display: flex;
        overflow: hidden;
        padding: 4px 15px 0 15px;
    }

    li {
        cursor: pointer;
        display: inline-block;
        float: left;
        position: relative;
        flex-shrink: 0;
        width: auto;
        padding: 7px 7px 7px 12px;
        border: 1px solid transparent;
        border-bottom: none;
        border-radius: 3px 3px 0 0;
        background-color: transparent;
        margin: 0 -1px 0 0;

        &:hover {
            border-color: rgba(0, 0, 0, 0.075);
            background-color: transparent;
            color: var(--el-color-white) !important;

            a {
                background: none;
                z-index: 1;
            }
        }

        &.active {
            background-color: var(--el-bg-color-overlay);
            border-color: var(--el-border-color-dark);
            border-bottom: 1px solid transparent;
            box-shadow:
                -2px -2px 2px -2px rgba(0, 0, 0, 0.15),
                2px -2px 2px -2px rgba(0, 0, 0, 0.15);

            a {
                cursor: default;
                z-index: 2;
            }

            i {
                &.tab-close {
                    @include flex-center;
                    cursor: pointer;
                }
            }
        }

        a {
            @include flex-center;
            text-decoration: none;
            overflow: hidden;
            border-radius: 0;
            z-index: 0;

            &:hover,
            &:focus {
                color: var(--el-text-color-regular);
            }

            &::after {
                content: ' ';
                position: absolute;
                right: -1px;
                top: 7px;
                bottom: 0;
                width: 1px;
                height: 18px;
                background-image: linear-gradient(transparent, rgba(0, 0, 0, 0.25), transparent);
            }
        }

        span {
            position: relative;
            top: 0;
            right: 0;
            left: 0;
            margin: 0;
            padding: 0 5px 0 3px;
            display: inline-block;
            overflow: hidden;
            font-size: 14px;
            line-height: 21px;
            text-overflow: ellipsis;
            text-align: center;
            text-decoration: none;
            white-space: nowrap;
            flex-grow: 1;
            flex-shrink: 1;
            flex-basis: 0;
        }

        i {
            &.tab-icon {
                margin-bottom: 2px;
            }

            &.tab-close {
                border-radius: 0;
                width: 14px;
                height: 14px;
                display: none;

                &:hover {
                    background: rgba(203, 41, 0, 0.9);
                    color: var(--el-color-white);
                    cursor: pointer;
                }
            }
        }

        &.hide {
            display: none !important;
        }

        &.sortable-ghost {
            opacity: 0;
        }
    }
}
