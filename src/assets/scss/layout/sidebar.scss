// 主布局sidebar样式
// <AUTHOR> <<PERSON>yu<PERSON>@live.com>
// @Datetime 2024-12-13

@use '../global/mixins' as *;

/* 左侧菜单 */
.layout-side-split {
    width: var(--main-sidebar-width);
    background: var(--el-color-primary);
    flex-shrink: 0;
    display: flex;
    flex-flow: column;

    .el-menu {
        width: 100%;

        .el-menu-item {
            @include flex-center;
            height: 80px;
            color: #fff;

            &:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            &.active {
                background: rgba(0, 0, 0, 0.3);
            }

            .menu-item-content {
                @include flex-center;
                flex-direction: column;

                .el-icon {
                    font-size: 24px;
                    margin-bottom: 5px;
                }

                span {
                    line-height: 1.2;
                }
            }
        }
    }

    // 顶部logo(.layout-side-split-top)
    &-top {
        @include flex-center;
        height: 100px;

        a {
            @include flex-center;
            width: 100%;
            height: 100%;
        }

        .logo {
            vertical-align: bottom;
        }
    }

    // 滚动条(.layout-side-split-scroll)
    &-scroll {
        @include scrollbar(5px);
        @include transition-base;
        overflow: auto;
        overflow-x: hidden;
        height: 100%;
        flex: 1;
    }
}

.layout-side {
    display: flex;
    flex-flow: column;
    flex-shrink: 0;
    width: 210px;
    background: rgba(0, 0, 0, 0.015);
    box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
    border-right: 1px solid var(--el-border-color-dark);
    transition: width 0.3s;

    &.isCollapse {
        width: 65px;
    }

    .layout-side-top {
        border-bottom: 1px solid var(--el-border-color-dark);
        height: 40px;
        line-height: 40px;
        background: rgba(0, 0, 0, 0.075);

        h2 {
            padding: 0 20px;
            font-weight: normal;
            font-size: 16px;
            color: var(--el-text-color-secondary);

            .el-icon {
                top: 2px;
                margin-right: 5px;
            }
        }
    }

    .layout-side-scroll {
        overflow: auto;
        overflow-x: hidden;
        flex: 1;
    }

    .layout-side-bottom {
        @include flex-center;
        background: rgba(0, 0, 0, 0.035);
        border-top: 1px solid var(--el-border-color-dark);
        height: 51px;
        line-height: 51px;
        color: var(--el-text-color-regular);
        cursor: pointer;

        i {
            font-size: 16px;
        }

        &:hover {
            color: var(--el-text-color-primary);
        }
    }
}
