// 系统loading样式
// <AUTHOR> <<PERSON>yu<PERSON>@live.com>
// @Datetime 2024-12-13

@use '../global/vars';
@use '../theme/main';

.app-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background: val(--color-background);

    &__loader {
        box-sizing: border-box;
        width: 35px;
        height: 35px;
        border: 5px solid transparent;
        border-top-color: rgba(
            var(--inverse-color-rgb-red),
            var(--inverse-color-rgb-green),
            var(--inverse-color-rgb-blue)
        );
        border-radius: 50%;
        animation: 0.5s loader linear infinite;
        position: relative;

        &:before {
            box-sizing: border-box;
            content: '';
            display: block;
            width: inherit;
            height: inherit;
            position: absolute;
            top: -5px;
            left: -5px;
            border: 5px solid #cccccc;
            border-radius: 50%;
            opacity: 0.5;
        }
    }

    &__title {
        font-size: 24px;
        color: rgba($color: #000000, $alpha: 0.65);
        margin-top: 30px;
    }
}

html[data-theme='dark'] {
    .app-loading {
        &__title {
            color: rgba($color: #ffffff, $alpha: 0.65);
        }
    }
}

@keyframes loader {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
