import type { DirectiveBinding } from 'vue';
import { ElMessage } from 'element-plus';

// 扩展HTMLElement类型以包含自定义属性
interface CopyElement extends HTMLElement {
    $value: string;
    handler: () => void;
}

export default {
    mounted(el: CopyElement, binding: DirectiveBinding<string>) {
        el.$value = binding.value;
        el.handler = () => {
            const textarea = document.createElement('textarea');
            textarea.readOnly = true;
            textarea.style.position = 'absolute';
            textarea.style.left = '-9999px';
            textarea.value = el.$value;
            document.body.appendChild(textarea);
            textarea.select();
            textarea.setSelectionRange(0, textarea.value.length);
            const result = document.execCommand('Copy');
            if (result) {
                ElMessage.success('复制成功');
            }
            document.body.removeChild(textarea);
        };
        el.addEventListener('click', el.handler);
    },
    updated(el: CopyElement, binding: DirectiveBinding<string>) {
        el.$value = binding.value;
    },
    unmounted(el: CopyElement) {
        el.removeEventListener('click', el.handler);
    }
};
