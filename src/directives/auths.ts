import type { DirectiveBinding } from 'vue';
import { permissionAll } from '@/utils/permission';
import tool from '@/utils/tool';
import storageConfig from '@/config/storage';

/**
 * 用户权限指令
 * @directive 单个权限验证（v-auth="'xxx'"）
 * @directive 多个权限验证，满足一个则显示（v-auths="['xxx','xxx']"）
 * @directive 多个权限验证，全部满足则显示（v-auths-all="['xxx','xxx']"）
 */
export default {
    mounted(el: HTMLElement, binding: DirectiveBinding<string[]>) {
        if (permissionAll()) {
            return;
        }
        const permissions: string[] = tool.data.get(storageConfig.vars.userPermission);
        let flag = false;
        permissions.forEach(val => {
            binding.value.forEach(v => {
                if (val === v) flag = true;
            });
        });
        if (!flag) {
            el.parentNode?.removeChild(el);
        }
    }
};
