import type { Router, RouteRecordRaw, RouteLocationNormalized, NavigationGuardNext } from 'vue-router';
import { createRouter, createWebHashHistory } from 'vue-router';
import { ElNotification } from 'element-plus';
import type { AppConfig } from '@/config';
import config from '@/config';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import account from '@/utils/account';
import type { Tool } from '@/utils/tool';
import tool from '@/utils/tool';
import type { StorageConfig } from '@/config/storage';
import storageConfig from '@/config/storage';
import systemRouter from './systemRouter';
import userRoutes from '@/config/route';
import { afterEach, beforeEach } from './scrollBehavior';
import { useViewTabsStore } from '@/stores/viewTabs';

// 路由元信息类型
interface RouteMeta {
    title?: string;
    role?: string[];
    fullpage?: boolean;
    type?: string;
    url?: string;
    breadcrumb?: RouteRecordRaw[];
}

// 扩展路由记录类型
interface ExtendedRouteRecordRaw extends Omit<RouteRecordRaw, 'meta' | 'children'> {
    meta?: RouteMeta;
    children?: ExtendedRouteRecordRaw[];
    component?: string | (() => Promise<any>);
}

// 扩展路由器类型
interface ExtendedRouter extends Router {
    sc_getMenu: () => ExtendedRouteRecordRaw[];
}

//系统路由
const routes: RouteRecordRaw[] = systemRouter;
const modules = import.meta.glob('@/views/**/*.vue');

let routeMap: Record<string, () => Promise<any>> = {};

for (let key in modules) {
    if (modules.hasOwnProperty(key)) {
        // 判断是否index.vue结尾
        let key_ = key.endsWith('/index.vue') ? key.replace('/index.vue', '') : key.replace('.vue', '');
        routeMap[key_] = modules[key];
    }
}

//系统特殊路由
const routes_404: RouteRecordRaw = {
    path: '/:pathMatch(.*)*',
    meta: { hidden: true },
    component: () => import('@/layout/other/404.vue')
};
let routes_404_r: (() => void) = () => {};

const router = createRouter({
    history: createWebHashHistory(import.meta.env.VITE_BASE_URL),
    routes: routes
}) as ExtendedRouter;

//设置标题
document.title = config.APP_NAME;

//判断是否已加载过动态/静态路由
let isGetRouter: boolean = false;

router.beforeEach(async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
    NProgress.start();
    //动态标题
    document.title = to.meta?.title ? `${to.meta.title} - ${config.APP_NAME}` : `${config.APP_NAME}`;

    let accessToken: string | null = tool.cookie.get(storageConfig.vars.accessToken);

    // 处理登录页逻辑
    if (to.path === '/login') {
        //删除路由(替换当前layout路由)
        router.addRoute(routes[0]);
        //删除路由(404)
        routes_404_r();
        isGetRouter = false;
        next();
        return;
    }

    // 处理无token情况
    if (!accessToken) {
        next({
            path: '/login',
            query: {
                redirect: to.fullPath
            }
        });
        return;
    }

    //整页路由处理
    if (to.meta?.fullpage) {
        to.matched = [to.matched[to.matched.length - 1]];
    }

    //加载动态/静态路由
    if (!isGetRouter) {
        let apiMenu: ExtendedRouteRecordRaw[] = tool.data.get(storageConfig.vars.userMenu) || [];
        // 从缓存中取出用户信息
        let userInfo: any = account.userInfo.get() || {};
        let userMenu: ExtendedRouteRecordRaw[] = treeFilter(userRoutes, (node: ExtendedRouteRecordRaw) => {
            return node.meta?.role ? node.meta.role.filter((item: string) => userInfo.role.indexOf(item) > -1).length > 0 : true;
        });
        let menu: ExtendedRouteRecordRaw[] = [...userMenu, ...apiMenu];
        var menuRouter: RouteRecordRaw[] = filterAsyncRouter(menu);
        menuRouter = flatAsyncRoutes(menuRouter);

        // 先添加所有路由
        menuRouter.forEach((item: RouteRecordRaw) => {
            router.addRoute('layout', item);
        });

        routes_404_r = router.addRoute(routes_404);
        isGetRouter = true;

        // 关键修改: 如果进入的是根路径，重定向到 dashboard
        if (to.path === '/') {
            next({ path: '/dashboard', replace: true });
            return;
        }

        // 否则重新进入当前路由
        next({ ...to, replace: true });
        return;
    }

    // 检查标签页数量限制，阻止超出限制的页面打开
    const viewTabsStore = useViewTabsStore();
    // 如果方法存在且不能添加标签（当canAddTab返回false时表示已达到上限）
    if (viewTabsStore.canAddTab && !viewTabsStore.canAddTab(to)) {
        // 标签已达上限且不是当前页面
        const message = config.MAX_TABS === 0
            ? '标签页数量已达到上限，请关闭部分标签页后再打开新页面'
            : `标签页数量已达到上限(${config.MAX_TABS}个)，请关闭部分标签页后再打开新页面`;

        ElNotification.error({
            title: '标签数量限制',
            message: message
        });
        NProgress.done();
        next(false); // 阻止导航
        return;
    }

    // 添加路由存在性检查
    if (to.matched.length === 0) {
        from.name ? next({ name: from.name }) : next('/404');
        return;
    }

    beforeEach(to, from);
    next();
});

router.afterEach((to, from) => {
    afterEach(to, from);
    NProgress.done();
});

router.onError(error => {
    NProgress.done();
    ElNotification.error({
        title: '路由错误',
        message: error.message
    });
});

//入侵追加自定义方法、对象
router.sc_getMenu = (): ExtendedRouteRecordRaw[] => {
    var apiMenu: ExtendedRouteRecordRaw[] = tool.data.get(storageConfig.vars.userMenu) || [];
    // 从缓存中取出用户信息
    let userInfo: any = account.userInfo.get() || {};
    let userMenu: ExtendedRouteRecordRaw[] = treeFilter(userRoutes, (node: ExtendedRouteRecordRaw) => {
        return node.meta?.role ? node.meta.role.filter((item: string) => userInfo.role.indexOf(item) > -1).length > 0 : true;
    });
    return [...userMenu, ...apiMenu];
};

//转换
function filterAsyncRouter(routerMap: ExtendedRouteRecordRaw[]): RouteRecordRaw[] {
    const accessedRouters: RouteRecordRaw[] = [];
    routerMap.forEach((item: ExtendedRouteRecordRaw) => {
        item.meta = item.meta ? item.meta : {};
        //处理外部链接特殊路由
        if (item.meta.type === 'iframe') {
            item.meta.url = item.path;
            item.path = `/i/${item.name}`;
        }
        //MAP转路由对象
        var route: RouteRecordRaw = {
            path: item.path,
            name: item.name,
            meta: item.meta,
            redirect: item.redirect,
            children: item.children ? filterAsyncRouter(item.children) : undefined,
            component: loadComponent(item.component as string)
        };
        accessedRouters.push(route);
    });
    return accessedRouters;
}

function loadComponent(component?: string): () => Promise<any> {
    if (component) {
        for (const path in modules) {
            const dir = path.split('views/')[1].split('.vue')[0];
            if (dir === component || dir === component + '/index') {
                return () => modules[path]();
            }
        }
        // 当组件不存在时，返回提示页面
        return () => import(`@/layout/other/ComponentNotFound.vue`);
    } else {
        return () => import(`@/layout/other/empty.vue`);
    }
}

//路由扁平化
function flatAsyncRoutes(routes: RouteRecordRaw[], breadcrumb: RouteRecordRaw[] = []): RouteRecordRaw[] {
    let res: RouteRecordRaw[] = [];
    routes.forEach((route: RouteRecordRaw) => {
        const tmp = { ...route };
        if (tmp.children) {
            let childrenBreadcrumb = [...breadcrumb];
            childrenBreadcrumb.push(route);
            let tmpRoute = { ...route };
            if (tmpRoute.meta) {
                tmpRoute.meta.breadcrumb = childrenBreadcrumb;
            }
            delete tmpRoute.children;
            res.push(tmpRoute);
            let childrenRoutes = flatAsyncRoutes(tmp.children, childrenBreadcrumb);
            childrenRoutes.map((item: RouteRecordRaw) => {
                res.push(item);
            });
        } else {
            let tmpBreadcrumb = [...breadcrumb];
            tmpBreadcrumb.push(tmp);
            if (tmp.meta) {
                tmp.meta.breadcrumb = tmpBreadcrumb;
            }
            res.push(tmp);
        }
    });
    return res;
}

//过滤树
function treeFilter<T extends ExtendedRouteRecordRaw>(tree: T[], func: (node: T) => boolean): T[] {
    return tree
        .map((node: T) => ({ ...node }))
        .filter((node: T) => {
            node.children = node.children && treeFilter(node.children, func);
            return func(node) || (node.children && node.children.length);
        });
}

export default router;
