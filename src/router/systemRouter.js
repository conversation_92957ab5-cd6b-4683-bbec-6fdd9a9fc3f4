import type { RouteRecordRaw } from 'vue-router';
import config from '@/config';

// 系统路由
const routes: RouteRecordRaw[] = [
    // 解决浏览器warn提示找不到路由的问题
    {
        path: '/:pathMatch(.*)*',
        component: () => import('@/layout/other/404.vue'),
        meta: { hidden: true }
    },
    {
        name: 'layout',
        path: '/',
        component: () => import('@/layout/index.vue'),
        redirect: config.DASHBOARD_URL || '/dashboard',
        children: []
    },
    {
        path: '/login',
        component: () => import('@/views/account/login.vue'),
        meta: {
            title: '登录'
        }
    },
    {
        path: '/user_register',
        component: () => import('@/views/account/userRegister.vue'),
        meta: {
            title: '注册'
        }
    },
    {
        path: '/reset_password',
        component: () => import('@/views/account/resetPassword.vue'),
        meta: {
            title: '重置密码'
        }
    }
];

export default routes;
