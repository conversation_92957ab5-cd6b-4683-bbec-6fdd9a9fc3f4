import config from '@/config';

//系统路由
const routes = [
    // 解决浏览器warn提示找不到路由的问题
    {
        path: '/:pathMatch(.*)*',
        component: () => import('@/layout/other/404.vue'),
        hidden: true
    },
    {
        name: 'layout',
        path: '/',
        component: () => import('@/layout'),
        redirect: config.DASHBOARD_URL || '/dashboard'
    },
    {
        path: '/login',
        component: () => import('@/views/account/login'),
        meta: {
            title: '登录'
        }
    },
    {
        path: '/user_register',
        component: () => import('@/views/account/userRegister'),
        meta: {
            title: '注册'
        }
    },
    {
        path: '/reset_password',
        component: () => import('@/views/account/resetPassword'),
        meta: {
            title: '重置密码'
        }
    }
];

export default routes;
