import { h, resolveComponent, defineComponent } from 'vue';

export default defineComponent({
    render() {
        return h(
            resolveComponent('el-table-column'),
            {
                index: this.index,
                ...this.$attrs
            },
            this.$slots
        );
    },
    methods: {
        index(index: number): number | undefined {
            if (this.$attrs.type === 'index') {
                const page = (this.$parent as any)?.$parent?.currentPage || 1;
                const pageSize = (this.$parent as any)?.$parent?.pageSize || 10;
                return (page - 1) * pageSize + index + 1;
            }
        }
    }
});
