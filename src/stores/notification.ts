import { defineStore } from 'pinia';
import notification from '@/api/model/notification';
import { usePingStore } from '@/stores/ping';
import { useLockscreenStore } from '@/stores/lockscreen';

let unreadCountTimer = null;

export const useNotificationStore = defineStore('notification', {
    state: () => ({
        unreadCount: 0,
        lastUpdateTime: 0
    }),

    actions: {
        async getUnreadCount() {
            const pingStore = usePingStore();
            const lockscreenStore = useLockscreenStore();
            // 无心跳时或锁屏时不再发起请求
            if (!pingStore.isRunning || lockscreenStore.isLocked) {
                return;
            }

            // 防止300ms内重复请求
            const now = Date.now();
            if (now - this.lastUpdateTime < 300) {
                return;
            }

            if (unreadCountTimer) {
                clearTimeout(unreadCountTimer);
            }

            unreadCountTimer = setTimeout(async () => {
                try {
                    const { status, data } = await notification.unreadCount.get();
                    if (status === 1 && data?.count >= 0) {
                        this.unreadCount = data.count;
                        this.lastUpdateTime = Date.now();
                    }
                } catch (error) {
                    console.error('获取未读消息数量失败:', error);
                }
                unreadCountTimer = null;
            }, 300);
        }
    }
});
