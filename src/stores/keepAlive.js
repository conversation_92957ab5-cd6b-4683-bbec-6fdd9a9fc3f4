import { defineStore } from 'pinia';

// KeepAlive状态类型定义
export interface KeepAliveState {
    keepLiveRoute: string[];
    routeKey: string | null;
    routeShow: boolean;
}

export const useKeepAliveStore = defineStore('keepAliveStore', {
    state: (): KeepAliveState => {
        return {
            keepLiveRoute: [],
            routeKey: null,
            routeShow: true
        };
    },
    getters: {},
    actions: {
        pushKeepLive(component: string): void {
            if (!this.keepLiveRoute.includes(component)) {
                this.keepLiveRoute.push(component);
            }
        },
        removeKeepLive(component: string): void {
            const index = this.keepLiveRoute.indexOf(component);
            if (index !== -1) {
                this.keepLiveRoute.splice(index, 1);
            }
        },
        clearKeepLive(): void {
            this.keepLiveRoute = [];
        },
        setRouteKey(key: string | null): void {
            this.routeKey = key;
        },
        setRouteShow(show: boolean): void {
            this.routeShow = show;
        }
    }
});
