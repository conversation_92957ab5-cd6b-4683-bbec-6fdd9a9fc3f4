import { defineStore } from 'pinia';
import type { RouteLocationNormalized } from 'vue-router';

// iframe路由项类型
export interface IframeRoute {
    path: string;
    meta: {
        url: string;
        title?: string;
        [key: string]: any;
    };
    [key: string]: any;
}

// iframe状态类型定义
export interface IframeState {
    iframeList: IframeRoute[];
}

export const useIframeStore = defineStore('iframeStore', {
    state: (): IframeState => {
        return {
            iframeList: []
        };
    },
    getters: {},
    actions: {
        setIframeList(route: IframeRoute): void {
            this.iframeList = [];
            this.iframeList.push(route);
        },
        pushIframeList(route: IframeRoute): void {
            const target = this.iframeList.find(item => item.path === route.path);
            if (!target) {
                this.iframeList.push(route);
            }
        },
        removeIframeList(route: IframeRoute): void {
            this.iframeList.forEach((item, index) => {
                if (item.path === route.path) {
                    this.iframeList.splice(index, 1);
                }
            });
        },
        refreshIframe(route: IframeRoute): void {
            try {
                this.iframeList.forEach(item => {
                    if (item.path === route.path) {
                        const url = route.meta.url;
                        item.meta.url = '';
                        setTimeout(() => {
                            item.meta.url = url;
                        }, 200);
                    }
                });
            } catch (err) {
                console.error('刷新iframe失败:', err);
            }
        },
        clearIframeList(): void {
            this.iframeList = [];
        }
    }
});
