import { defineStore } from 'pinia';
import tool from '@/utils/tool';
import storageConfig from '@/config/storage';

export const useLockscreenStore = defineStore('lockscreenStore', {
    state: () => ({
        // 系统是否锁屏
        isLocked: false,
        // 自动锁屏时间(分钟)
        autoLockTime: parseInt(tool.data.get(storageConfig.vars.autoLockscreen)) || 0
    }),

    actions: {
        // 设置锁屏状态
        setLockStatus(status) {
            this.isLocked = status;
            if (status) {
                tool.cookie.set(storageConfig.vars.lockscreen, 1);
            } else {
                tool.cookie.remove(storageConfig.vars.lockscreen);
            }
        },

        // 设置自动锁屏时间
        setAutoLockTime(minutes) {
            this.autoLockTime = minutes;
            if (minutes > 0) {
                tool.data.set(storageConfig.vars.autoLockscreen, minutes);
            } else {
                tool.data.remove(storageConfig.vars.autoLockscreen);
            }
        }
    }
});
