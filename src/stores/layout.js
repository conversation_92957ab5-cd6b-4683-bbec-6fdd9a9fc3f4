import { defineStore } from 'pinia';
import theme from '@/utils/theme';
import config from '@/config';
import storageConfig from '@/config/storage';
import tool from '@/utils/tool';

export const useLayoutStore = defineStore('theme', {
    state: () => ({
        appLayoutMobile: false,
        appPrimaryColor: theme.appPrimaryColor,
        appLayout: tool.data.get(storageConfig.vars.appLayout) || 'default',
        appMenuCollapse: tool.data.get(storageConfig.vars.appMenuCollapse) || true,
        appShowTabs: tool.data.get(storageConfig.vars.appShowTabs) ?? true,
        // 优先使用用户设置的主题模式，如果没有则使用配置的默认主题模式
        appThemeMode: tool.data.get(storageConfig.vars.appThemeMode) || config.DEFAULT_THEME_MODE
    }),
    getters: {
        /**
         * 是否为深色模式
         * @returns {boolean}
         */
        isDarkMode() {
            if (this.appThemeMode === 'dark') return true;
            if (this.appThemeMode === 'light') return false;
            return window.matchMedia('(prefers-color-scheme: dark)').matches;
        },
        /**
         * 是否为移动布局
         * @returns {(function(): boolean)|*}
         */
        isColorPickerDisabled() {
            return this.isDarkMode;
        }
    },
    actions: {
        /**
         * 设置布局是否为移动布局
         * @param value
         */
        setAppLayoutMobile(value) {
            this.appLayoutMobile = value;
        },
        /**
         * 设置布局主色彩
         * @param color
         */
        setAppPrimaryColor(color) {
            // 在深色模式下或禁用时不允许设置主题色
            if (this.isDarkMode || !color) {
                this.appPrimaryColor = null;
                theme.removeThemeColor();
                tool.data.remove(storageConfig.vars.appPrimaryColor);
                return;
            }
            color = color.toLowerCase();
            // 如果是默认颜色则移除缓存
            if (color === config.COLOR || color === config.COLOR_LIST[0]) {
                tool.data.remove(storageConfig.vars.appPrimaryColor);
            }
            this.appPrimaryColor = color;
            theme.updateThemeColor(color, true);
        },
        /**
         * 设置布局
         * @param appLayout
         */
        setAppLayout(appLayout) {
            this.appLayout = appLayout;
            tool.data.set(storageConfig.vars.appLayout, appLayout);
        },
        /**
         * 设置侧边栏是否折叠
         */
        setAppMenuCollapse() {
            this.appMenuCollapse = !this.appMenuCollapse;
            tool.data.set(storageConfig.vars.appMenuCollapse, this.appMenuCollapse);
        },
        /**
         * 设置是否显示标签页
         */
        setAppShowTabs() {
            this.appShowTabs = !this.appShowTabs;
            tool.data.set(storageConfig.vars.appShowTabs, this.appShowTabs);
        },
        /**
         * 设置主题模式
         * @param mode
         */
        setAppThemeMode(mode) {
            this.appThemeMode = mode;
            // 当 mode 为 auto 时不保存缓存
            if (mode !== 'auto') {
                tool.data.set(storageConfig.vars.appThemeMode, mode);
            } else {
                tool.data.remove(storageConfig.vars.appThemeMode);
            }
            document.documentElement.setAttribute('data-theme', mode);
            theme.setAppThemeMode(mode);
        }
    }
});
