import { defineStore } from 'pinia';
import account from '@/api/model/account';
import storageConfig from '@/config/storage';
import tool from '@/utils/tool';

export const usePingStore = defineStore('ping', {
    state: () => ({
        pingTimer: null,
        pingInterval: 10000, // 10秒
        isRunning: false // 是否已经在ping
    }),

    actions: {
        async startPing() {
            let accessToken = tool.cookie.get(storageConfig.vars.accessToken);

            // 检查是否已登录
            if (!accessToken) {
                return;
            }

            // 如果已经在运行，先停止
            if (this.isRunning || this.pingTimer) {
                this.stopPing();
            }

            // 标记为运行状态
            this.isRunning = true;

            // 立即执行一次
            try {
                await account.ping.get();
            } catch (error) {
                console.error('Initial ping failed:', error);
            }

            // 设置定时器
            this.pingTimer = setInterval(async () => {
                try {
                    await account.ping.get();
                } catch (error) {
                    console.error('Ping failed:', error);
                }
            }, this.pingInterval); // 每10秒ping一次
        },

        // 停止心跳检测
        stopPing() {
            if (this.pingTimer) {
                clearInterval(this.pingTimer);
                this.pingTimer = null;
            }
            this.isRunning = false;
        }
    }
});
