import { defineStore } from 'pinia';
import config from '@/config';

export const useGlobalStore = defineStore('globalStore', {
    state: () => {
        return {
            // 默认是否显示了用户登录框（非登录页，仅限登录框）
            userLoginDialogVisible: false,
            // 用户token是否已过期
            userTokenExpired: false,
            // 用户是否需要重置密码
            userResetPassword: false,
            // api接口默认地址
            apiDomain: config.API_URL,
            // 上次更新用户信息时间戳
            userInfoLastUpdateTime: 0
        };
    },
    getters: {},
    actions: {
        /**
         * 全局设置是否显示登录对话框
         * @param value
         * @constructor
         */
        SET_userLoginDialogVisible(value) {
            this.userLoginDialogVisible = value;
        },
        /**
         * 全局设置用户是否过期
         * @param value
         * @constructor
         */
        SET_userTokenExpired(value) {
            this.userTokenExpired = value;
        },
        /**
         * 全局设置用户修改密码状态
         * @param value
         * @constructor
         */
        SET_userResetPassword(value) {
            this.userResetPassword = value;
        },
        /**
         * 全局设置是否更新用户信息
         * @param value
         * @constructor
         */
        SET_userInfoLastUpdateTime(value) {
            this.userInfoLastUpdateTime = value;
        }
    }
});
