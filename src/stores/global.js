import { defineStore } from 'pinia';
import config from '@/config';

// 全局状态类型定义
export interface GlobalState {
    // 默认是否显示了用户登录框（非登录页，仅限登录框）
    userLoginDialogVisible: boolean;
    // 用户token是否已过期
    userTokenExpired: boolean;
    // 用户是否需要重置密码
    userResetPassword: boolean;
    // api接口默认地址
    apiDomain: string;
    // 上次更新用户信息时间戳
    userInfoLastUpdateTime: number;
}

export const useGlobalStore = defineStore('globalStore', {
    state: (): GlobalState => {
        return {
            // 默认是否显示了用户登录框（非登录页，仅限登录框）
            userLoginDialogVisible: false,
            // 用户token是否已过期
            userTokenExpired: false,
            // 用户是否需要重置密码
            userResetPassword: false,
            // api接口默认地址
            apiDomain: config.API_URL,
            // 上次更新用户信息时间戳
            userInfoLastUpdateTime: 0
        };
    },
    getters: {},
    actions: {
        /**
         * 全局设置是否显示登录对话框
         * @param value 是否显示登录对话框
         */
        SET_userLoginDialogVisible(value: boolean): void {
            this.userLoginDialogVisible = value;
        },
        /**
         * 全局设置用户是否过期
         * @param value 用户token是否过期
         */
        SET_userTokenExpired(value: boolean): void {
            this.userTokenExpired = value;
        },
        /**
         * 全局设置用户修改密码状态
         * @param value 是否需要重置密码
         */
        SET_userResetPassword(value: boolean): void {
            this.userResetPassword = value;
        },
        /**
         * 全局设置是否更新用户信息
         * @param value 用户信息更新时间戳
         */
        SET_userInfoLastUpdateTime(value: number): void {
            this.userInfoLastUpdateTime = value;
        }
    }
});
