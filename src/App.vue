<template>
    <startup-loading v-if="loading"></startup-loading>
    <el-config-provider v-else :locale="locale" :size="config.size" :zIndex="config.zIndex" :button="config.button">
        <router-view></router-view>
    </el-config-provider>
</template>

<script>
import account from '@/utils/account';
import { checkBrowserVersion } from '@/utils/browser';
import storageConfig from '@/config/storage';
import theme from '@/utils/theme.js';
import StartupLoading from '@/components/StartupLoading.vue';

export default {
    name: 'App',
    components: {
        StartupLoading
    },
    data() {
        return {
            loading: true,
            isFirstRoute: true,
            config: {
                size: 'default',
                zIndex: 2000,
                button: {
                    autoInsertSpace: false
                }
            }
        };
    },
    computed: {
        locale() {
            return this.$i18n.messages[this.$i18n.locale].el;
        }
    },
    async created() {
        // 检查浏览器版本
        if (!checkBrowserVersion()) {
            // 显示浏览器版本提示...
        }

        // 给容器添加一个layout标识
        document.getElementById('app').classList.add('layout');

        // 初始化主题
        theme.initTheme();
    },
    mounted() {
        // 仅当有token时才启动心跳
        if (this.$TOOL.cookie.get(storageConfig.vars.accessToken)) {
            account.startHeartBeat();
        }

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', this.handleVisibilityChange);
    },
    unmounted() {
        // 清理监听器
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);

        // 停止心跳检测
        account.stopHeartBeat();
    },
    methods: {
        /**
         * 监听页面可见性变化
         */
        handleVisibilityChange() {
            // 仅当有token时才处理心跳
            if (!this.$TOOL.cookie.get(this.$CONFIG.TOKEN_NAME)) {
                return;
            }

            if (document.visibilityState === 'visible') {
                // 页面可见时恢复心跳
                setTimeout(() => {
                    account.startHeartBeat();
                }, 1000); // 延迟1秒启动，避免快速切换导致的重复请求
            } else {
                // 页面隐藏时停止心跳
                account.stopHeartBeat();
            }
        },

        /**
         * 处理页面加载完成
         */
        handlePageLoaded() {
            // 确保只处理首次加载
            if (this.isFirstRoute) {
                this.isFirstRoute = false;
                this.loading = false;
            }
        }
    },
    watch: {
        // 监听路由变化
        $route: {
            async handler(to, from) {
                // 首次加载时等待页面挂载完成
                if (this.isFirstRoute) {
                    await this.$nextTick();
                    // 给一个短暂延时确保页面完全渲染
                    setTimeout(() => {
                        this.handlePageLoaded();
                    }, 100);
                }
            },
            immediate: true
        }
    }
};
</script>

<style lang="scss">
@use '@/assets/scss/init.scss';
</style>
