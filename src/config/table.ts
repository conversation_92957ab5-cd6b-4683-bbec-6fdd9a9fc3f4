import tool from '@/utils/tool';

// 表格数据解析结果类型
export interface TableParseResult {
    data: any[];
    rows: any[];
    total: number;
    summary?: any;
    msg: string;
    code: number;
}

// 表格请求配置类型
export interface TableRequestConfig {
    page: string;
    pageSize: string;
    prop: string;
    order: string;
}

// 表格配置类型
export interface TableConfig {
    successCode: number;
    pageSize: number;
    pageSizes: number[];
    paginationLayout: string;
    parseData: (res: any) => TableParseResult;
    request: TableRequestConfig;
    columnSettingSave: (tableName: string, column: any[]) => Promise<boolean>;
    columnSettingGet: (tableName: string, column: any[]) => Promise<any[]>;
    columnSettingReset: (tableName: string, column: any[]) => Promise<any[]>;
}

// 数据表格配置
const tableConfig: TableConfig = {
    successCode: 0, // 请求完成代码
    pageSize: 20, // 表格每一页条数
    pageSizes: [10, 20, 30, 40, 50], // 表格可设置的一页条数
    paginationLayout: 'total, sizes, prev, pager, next, jumper', // 表格分页布局，可设置"total, sizes, prev, pager, next, jumper"
    parseData: function (res: any): TableParseResult {
        // 数据分析
        return {
            data: res.data, // 分析无分页的数据字段结构
            rows: res.data?.list, // 分析行数据字段结构
            total: res.data?.total, // 分析总数字段结构
            summary: res.data?.summary, // 分析合计行字段结构
            msg: res.message, // 分析描述字段结构
            code: res.code // 分析状态字段结构
        };
    },
    request: {
        // 请求规定字段
        page: 'page', // 规定当前分页字段
        pageSize: 'pageSize', // 规定一页条数字段
        prop: 'prop', // 规定排序字段名字段
        order: 'order' // 规定排序规格字段
    },
    /**
     * 自定义列保存处理
     * @param tableName scTable组件的props->tableName
     * @param column 用户配置好的列
     */
    columnSettingSave: function (tableName: string, column: any[]): Promise<boolean> {
        return new Promise(resolve => {
            setTimeout(() => {
                // 这里为了演示使用了session和setTimeout演示，开发时应用数据请求
                tool.session.set(tableName, column);
                resolve(true);
            }, 1000);
        });
    },
    /**
     * 获取自定义列
     * @param tableName scTable组件的props->tableName
     * @param column 组件接受到的props->column
     */
    columnSettingGet: function (tableName: string, column: any[]): Promise<any[]> {
        return new Promise(resolve => {
            // 这里为了演示使用了session和setTimeout演示，开发时应用数据请求
            const userColumn = tool.session.get(tableName);
            if (userColumn) {
                resolve(userColumn);
            } else {
                resolve(column);
            }
        });
    },
    /**
     * 重置自定义列
     * @param tableName scTable组件的props->tableName
     * @param column 组件接受到的props->column
     */
    columnSettingReset: function (tableName: string, column: any[]): Promise<any[]> {
        return new Promise(resolve => {
            // 这里为了演示使用了session和setTimeout演示，开发时应用数据请求
            setTimeout(() => {
                tool.session.remove(tableName);
                resolve(column);
            }, 1000);
        });
    }
};

export default tableConfig;
