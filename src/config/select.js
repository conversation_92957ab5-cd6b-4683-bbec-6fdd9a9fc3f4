import API from '@/api';

// 选择器解析结果类型
export interface SelectParseResult {
    data: any[];
    msg: string;
    code: number;
}

// 选择器请求配置类型
export interface SelectRequestConfig {
    name: string;
}

// 选择器属性配置类型
export interface SelectPropsConfig {
    label: string;
    value: string;
}

// 选择器配置类型
export interface SelectConfig {
    dicApiObj: any;
    parseData: (res: any) => SelectParseResult;
    request: SelectRequestConfig;
    props: SelectPropsConfig;
}

// 字典选择器配置
const selectConfig: SelectConfig = {
    dicApiObj: API.tenant.dic.get, // 获取字典接口对象
    parseData: function (res: any): SelectParseResult {
        return {
            data: res.data, // 分析行数据字段结构
            msg: res.message, // 分析描述字段结构
            code: res.code // 分析状态字段结构
        };
    },
    request: {
        name: 'name' // 规定搜索字段
    },
    props: {
        label: 'label', // 映射label显示字段
        value: 'value' // 映射value值字段
    }
};

export default selectConfig;
