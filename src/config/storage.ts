/**
 * 本地存储（cookie、localStorage、sessionStorage等）常用配置
 */

// 存储变量名类型定义
export interface StorageVars {
    accessToken: string;
    refreshToken: string;
    userInfo: string;
    userMenu: string;
    userPermission: string;
    lockscreen: string;
    autoLockscreen: string;
    myMods: string;
    searchHistory: string;
    // 布局相关
    appPrimaryColor: string;
    appLang: string;
    appThemeMode: string;
    appLayout: string;
    appMenuCollapse: string;
    appShowTabs: string;
}

// IndexedDB索引配置类型
export interface IndexConfig {
    name: string;
    keyPath: string;
    unique?: boolean;
}

// IndexedDB存储对象配置类型
export interface StoreConfig {
    name: string;
    keyPath: string;
    autoIncrement?: boolean;
    indexes?: IndexConfig[];
}

// IndexedDB配置类型
export interface IndexedDBConfig {
    dbName: string;
    dbVersion: number;
    storeConfigs: {
        deviceStatus: StoreConfig;
        quickMenu: StoreConfig;
        mqttReceived: StoreConfig;
        mqttSent: StoreConfig;
    };
}

// 存储配置类型
export interface StorageConfig {
    vars: StorageVars;
    indexedDB: IndexedDBConfig;
}

const storageConfig: StorageConfig = {
    /**
     * 本地数据变量名（cookie &localstorage）
     */
    vars: {
        accessToken: 'accessToken',
        refreshToken: 'refreshToken',
        userInfo: 'userInfo',
        userMenu: 'userMenu',
        userPermission: 'userPermission',
        lockscreen: 'lockscreen',
        autoLockscreen: 'auto_lockscreen',
        myMods: 'my-mods',
        searchHistory: 'search_history',
        // 布局相关
        appPrimaryColor: 'app_color',
        appLang: 'app_lang',
        appThemeMode: 'app_theme',
        appLayout: 'app_layout',
        appMenuCollapse: 'app_menu_collapse',
        appShowTabs: 'app_show_tabs'
    },
    /**
     * indexedDB存储对象配置
     */
    indexedDB: {
        // 数据库名称
        dbName: 'systemDB',
        // 数据库版本
        dbVersion: 1,
        // 存储对象详细配置
        storeConfigs: {
            // 设备状态存储对象
            deviceStatus: {
                name: 'device_status',
                keyPath: 'deviceId',
                indexes: [
                    { name: 'lastHeartbeat', keyPath: 'lastHeartbeat' }
                ]
            },
            // 快捷菜单存储对象
            quickMenu: {
                name: 'quick_menu',
                keyPath: 'id',
                autoIncrement: true
            },
            // MQTT接收消息存储对象
            mqttReceived: {
                name: 'mqtt_msg_received',
                keyPath: 'id',
                autoIncrement: true,
                indexes: [
                    { name: 'time', keyPath: 'time' }
                ]
            },
            // MQTT发送消息存储对象
            mqttSent: {
                name: 'mqtt_msg_sent',
                keyPath: 'id',
                autoIncrement: true,
                indexes: [
                    { name: 'time', keyPath: 'time' }
                ]
            }
        }
    }
};

export default storageConfig;
