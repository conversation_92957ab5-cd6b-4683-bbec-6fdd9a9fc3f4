import type { ApiResult, UploadFile } from '@/api/types';
import API from '@/api';

// 上传响应类型（实际的API响应格式）
interface UploadApiResponse extends ApiResult<{
    fileName: string;
    src: string;
}> {}

// 上传解析数据类型
interface UploadParseResult {
    code: number;
    fileName: string;
    src: string;
    msg: string;
}

// 上传配置类型
interface UploadConfig {
    apiObj: any; // 上传请求API对象
    filename: string; // form请求时文件的key
    successCode: number; // 请求完成代码
    maxSize: number; // 最大文件大小 默认10MB
    parseData: (res: UploadApiResponse) => UploadParseResult;
    apiObjFile: any; // 附件上传请求API对象
    maxSizeFile: number; // 最大文件大小 默认10MB
}

// 上传配置
const uploadConfig: UploadConfig = {
    apiObj: API.common.upload, // 上传请求API对象
    filename: 'file', // form请求时文件的key
    successCode: 0, // 请求完成代码
    maxSize: 10, // 最大文件大小 默认10MB
    parseData: function (res: UploadApiResponse): UploadParseResult {
        return {
            code: res.code, // 分析状态字段结构
            fileName: res.data.fileName, // 分析文件名称
            src: res.data.src, // 分析图片远程地址结构
            msg: res.message // 分析描述字段结构
        };
    },
    apiObjFile: API.common.uploadFile, // 附件上传请求API对象
    maxSizeFile: 10 // 最大文件大小 默认10MB
};

export default uploadConfig;
export type { UploadConfig, UploadParseResult };
