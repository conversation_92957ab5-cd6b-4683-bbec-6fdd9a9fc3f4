import API from '@/api';

// 上传解析结果类型
export interface UploadParseResult {
    code: number;
    fileName: string;
    src: string;
    msg: string;
}

// 上传配置类型
export interface UploadConfig {
    apiObj: any;
    filename: string;
    successCode: number;
    maxSize: number;
    parseData: (res: any) => UploadParseResult;
    apiObjFile: any;
    maxSizeFile: number;
}

// 上传配置
const uploadConfig: UploadConfig = {
    apiObj: API.common.upload, // 上传请求API对象
    filename: 'file', // form请求时文件的key
    successCode: 0, // 请求完成代码
    maxSize: 10, // 最大文件大小 默认10MB
    parseData: function (res: any): UploadParseResult {
        return {
            code: res.code, // 分析状态字段结构
            fileName: res.data.fileName, // 分析文件名称
            src: res.data.src, // 分析图片远程地址结构
            msg: res.message // 分析描述字段结构
        };
    },
    apiObjFile: API.common.uploadFile, // 附件上传请求API对象
    maxSizeFile: 10 // 最大文件大小 默认10MB
};

export default uploadConfig;
