// 租户状态类型
export interface TenantStatus {
    enable: number;
    disable: number;
}

// 租户类型
export interface TenantType {
    platform: number;    // 平台租户
    enterprise: number;  // 企业租户
    personal: number;    // 个人租户
}

// 租户数据隔离级别类型
export interface TenantIsolationLevel {
    database: number;    // 独立数据库
    schema: number;      // 共享数据库，独立Schema
    table: number;       // 共享数据库，共享Schema，独立表
}

// 租户配置项类型
export interface TenantConfigLimits {
    maxUsers: number;          // 最大用户数
    maxStorage: number;        // 最大存储空间(MB)
    maxProjects: number;       // 最大项目数
    maxBandwidth: number;      // 最大带宽(Mbps)
}

// 租户管理配置类型
export interface TenantConfig {
    status: TenantStatus;
    type: TenantType;
    isolationLevel: TenantIsolationLevel;
    config: TenantConfigLimits;
}

/**
 * 租户管理配置
 */
const tenantConfig: TenantConfig = {
    // 租户状态
    status: {
        enable: 1,
        disable: 0
    },
    // 租户类型
    type: {
        platform: 0,    // 平台租户
        enterprise: 1,  // 企业租户
        personal: 2     // 个人租户
    },
    // 租户数据隔离级别
    isolationLevel: {
        database: 0,    // 独立数据库
        schema: 1,      // 共享数据库，独立Schema
        table: 2        // 共享数据库，共享Schema，独立表
    },
    // 租户配置项
    config: {
        maxUsers: 100,          // 最大用户数
        maxStorage: 1024,       // 最大存储空间(MB)
        maxProjects: 10,        // 最大项目数
        maxBandwidth: 100       // 最大带宽(Mbps)
    }
};

export default tenantConfig;
