/**
 * 租户管理配置
 */
export default {
    // 租户状态
    status: {
        enable: 1,
        disable: 0
    },
    // 租户类型
    type: {
        platform: 0,    // 平台租户
        enterprise: 1,  // 企业租户
        personal: 2     // 个人租户
    },
    // 租户数据隔离级别
    isolationLevel: {
        database: 0,    // 独立数据库
        schema: 1,      // 共享数据库，独立Schema
        table: 2        // 共享数据库，共享Schema，独立表
    },
    // 租户配置项
    config: {
        maxUsers: 100,          // 最大用户数
        maxStorage: 1024,       // 最大存储空间(MB)
        maxProjects: 10,        // 最大项目数
        maxBandwidth: 100       // 最大带宽(Mbps)
    }
}
