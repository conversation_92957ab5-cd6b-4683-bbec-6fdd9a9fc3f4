//业务配置
//会合并至this.$CONFIG
//生产模式 public/config.js 同名key会覆盖这里的配置从而实现打包后的热更新
//为避免和SCUI框架配置混淆建议添加前缀 MY_
//全局可使用 this.$CONFIG.MY_KEY 访问

import type { AppConfig } from './index';

// 登录配置类型
export interface LoginConfig {
    // 是否显示平台二维码登录
    qrcodeLogin: boolean;
    // 是否显示账号登录
    accountLogin: boolean;
    // 是否显示短信验证登录
    smsVerifyLogin: boolean;
    // 显示微信登录二维码
    showWechatLoginQrcode: boolean;
    // 是否显示微信扫码登录
    wechatScanLogin: boolean;
    // 微信登录code
    wechatScanLoginCode: string;
}

// 业务配置类型（继承基础配置）
export interface MyConfig extends Partial<AppConfig> {
    LOGIN: LoginConfig;
}

const myConfig: MyConfig = {
    // API_URL: import.meta.env.NODE_ENV === 'development' && import.meta.env.VITE_APP_PROXY === 'true' ? "" : import.meta.env.VITE_APP_API_BASEURL,
    API_URL: 'http://api.heli.cn',

    // 是否加密localStorage, 为空不加密，可填写AES(模式ECB,移位Pkcs7)加密
    LS_ENCRYPTION: '',

    // localStorageAES加密秘钥，位数建议填写8的倍数
    LS_ENCRYPTION_key: 'XKSK9SC8CNXKS876',

    // 布局 默认：default | 通栏：header | 经典：menu | 功能坞：dock
    // dock将关闭标签和面包屑栏
    APP_LAYOUT: 'default',

    // 菜单是否折叠
    MENU_COLLAPSE: false,

    // 菜单是否启用手风琴效果
    MENU_UNIQUE_OPENED: false,

    // 是否开启多标签
    SHOW_TABS: true,

    // 语言
    APP_LANG: 'zh-cn',

    // 主题颜色
    COLOR: '#0f2549',

    // 默认主题模式 'auto' | 'light' | 'dark'
    DEFAULT_THEME_MODE: 'auto',

    // 全局登录配置
    LOGIN: {
        // 是否显示平台二维码登录
        qrcodeLogin: false,
        // 是否显示账号登录
        accountLogin: true,
        // 是否显示短信验证登录
        smsVerifyLogin: false,
        // 显示微信登录二维码
        showWechatLoginQrcode: false,
        // 是否显示微信扫码登录
        wechatScanLogin: false,
        // 微信登录code
        wechatScanLoginCode: ''
    }
};

export default myConfig;
