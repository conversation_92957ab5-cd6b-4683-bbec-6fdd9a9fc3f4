import API from '@/api';

// 工作流解析结果类型
export interface WorkflowParseResult {
    rows: any[];
    total?: number;
    msg: string;
    code: number;
}

// 工作流属性映射类型
export interface WorkflowPropsConfig {
    key: string;
    label: string;
    children?: string;
}

// 工作流请求配置类型
export interface WorkflowRequestConfig {
    page: string;
    pageSize: string;
    groupId: string;
    keyword: string;
}

// 工作流组织配置类型
export interface WorkflowGroupConfig {
    apiObj: any;
    parseData: (res: any) => WorkflowParseResult;
    props: WorkflowPropsConfig;
}

// 工作流用户配置类型
export interface WorkflowUserConfig {
    apiObj: any;
    pageSize: number;
    parseData: (res: any) => WorkflowParseResult;
    props: WorkflowPropsConfig;
    request: WorkflowRequestConfig;
}

// 工作流角色配置类型
export interface WorkflowRoleConfig {
    apiObj: any;
    parseData: (res: any) => WorkflowParseResult;
    props: WorkflowPropsConfig;
}

// 工作流配置类型
export interface WorkflowConfig {
    successCode: number;
    group: WorkflowGroupConfig;
    user: WorkflowUserConfig;
    role: WorkflowRoleConfig;
}

// 审批工作流人员/组织选择器配置
const workflowConfig: WorkflowConfig = {
    // 配置接口正常返回代码
    successCode: 0,
    // 配置组织
    group: {
        // 请求接口对象
        apiObj: API.system.department.list,
        // 接受数据字段映射
        parseData: function (res: any): WorkflowParseResult {
            return {
                rows: res.data,
                msg: res.message,
                code: res.code
            };
        },
        // 显示数据字段映射
        props: {
            key: 'id',
            label: 'label',
            children: 'children'
        }
    },
    // 配置用户
    user: {
        apiObj: API.system.user.list,
        pageSize: 20,
        parseData: function (res: any): WorkflowParseResult {
            return {
                rows: res.data.rows,
                total: res.data.total,
                msg: res.message,
                code: res.code
            };
        },
        props: {
            key: 'id',
            label: 'user'
        },
        request: {
            page: 'page',
            pageSize: 'pageSize',
            groupId: 'groupId',
            keyword: 'keyword'
        }
    },
    // 配置角色
    role: {
        // 请求接口对象
        apiObj: API.system.role.list,
        // 接受数据字段映射
        parseData: function (res: any): WorkflowParseResult {
            return {
                rows: res.data,
                msg: res.message,
                code: res.code
            };
        },
        // 显示数据字段映射
        props: {
            key: 'id',
            label: 'label',
            children: 'children'
        }
    }
};

export default workflowConfig;
