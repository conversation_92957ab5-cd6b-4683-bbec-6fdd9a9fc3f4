import API from '@/api';

// 文件选择器配置类型定义
export interface FileSelectUploadParseResult {
    id: string | number;
    fileName: string;
    url: string;
}

export interface FileSelectListParseResult {
    rows: any[];
    total: number;
    msg: string;
    code: number;
}

export interface FileSelectRequestConfig {
    page: string;
    pageSize: string;
    keyword: string;
    menuKey: string;
}

export interface FileSelectMenuProps {
    key: string;
    label: string;
    children: string;
}

export interface FileSelectFileProps {
    key: string;
    fileName: string;
    url: string;
}

export interface FileTypeConfig {
    icon: string;
    color: string;
}

export interface FileSelectConfig {
    apiObj: any;
    menuApiObj: any;
    listApiObj: any;
    successCode: number;
    maxSize: number;
    max: number;
    uploadParseData: (res: any) => FileSelectUploadParseResult;
    listParseData: (res: any) => FileSelectListParseResult;
    request: FileSelectRequestConfig;
    menuProps: FileSelectMenuProps;
    fileProps: FileSelectFileProps;
    files: Record<string, FileTypeConfig>;
}

// 文件选择器配置
const fileSelectConfig: FileSelectConfig = {
    apiObj: API.common.upload,
    menuApiObj: API.common.file.menu,
    listApiObj: API.common.file.list,
    successCode: 0,
    maxSize: 30,
    max: 99,
    uploadParseData: function (res: any): FileSelectUploadParseResult {
        return {
            id: res.data.id,
            fileName: res.data.fileName,
            url: res.data.src
        };
    },
    listParseData: function (res: any): FileSelectListParseResult {
        return {
            rows: res.data.rows,
            total: res.data.total,
            msg: res.message,
            code: res.code
        };
    },
    request: {
        page: 'page',
        pageSize: 'pageSize',
        keyword: 'keyword',
        menuKey: 'groupId'
    },
    menuProps: {
        key: 'id',
        label: 'label',
        children: 'children'
    },
    fileProps: {
        key: 'id',
        fileName: 'fileName',
        url: 'url'
    },
    files: {
        doc: {
            icon: 'sc-icon-file-word-2-fill',
            color: '#409eff'
        },
        docx: {
            icon: 'sc-icon-file-word-2-fill',
            color: '#409eff'
        },
        xls: {
            icon: 'sc-icon-file-excel-2-fill',
            color: '#67C23A'
        },
        xlsx: {
            icon: 'sc-icon-file-excel-2-fill',
            color: '#67C23A'
        },
        ppt: {
            icon: 'sc-icon-file-ppt-2-fill',
            color: '#F56C6C'
        },
        pptx: {
            icon: 'sc-icon-file-ppt-2-fill',
            color: '#F56C6C'
        }
    }
};

export default fileSelectConfig;
