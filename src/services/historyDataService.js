/**
 * 历史数据服务
 * 提供设备历史数据查询、导出等功能
 */

import API from '@/api';
import { ElMessage } from 'element-plus';
import logger from '@/utils/logger';

// 历史数据查询参数类型
export interface HistoryDataQueryParams {
    deviceId: string | number;
    layer?: number | null;
    startTime: string;
    endTime: string;
    dataTypes: string[];
    interval: string;
    page: number;
    pageSize: number;
}

// 历史数据导出参数类型
export interface HistoryDataExportParams {
    deviceId: string | number;
    layer?: number | null;
    startTime: string;
    endTime: string;
    dataTypes: string[];
    interval: string;
}

// 文件名生成参数类型
export interface FileNameParams {
    deviceName?: string;
    startTime: string;
    endTime: string;
    dataTypes: string[];
    layer?: number | null;
}

// 验证结果类型
export interface ValidationResult {
    valid: boolean;
    message: string;
}

// 原始查询参数类型
export interface RawQueryParams {
    deviceId: string | number;
    layer?: number | null;
    timeRange: [string, string];
    dataTypes: string[];
    interval?: string;
    page?: number;
    pageSize?: number;
}

// 数据类型配置类型
export interface DataTypeConfig {
    label: string;
    unit: string;
    color: string;
    decimals: number;
}

// 间隔选项类型
export interface IntervalOption {
    label: string;
    value: string;
}

class HistoryDataService {
    /**
     * 查询设备历史数据
     * @param {HistoryDataQueryParams} params 查询参数
     * @returns {Promise<any | null>} 查询结果
     */
    async queryHistoryData(params: HistoryDataQueryParams): Promise<any | null> {
        try {
            logger.group('查询历史数据');
            logger.info('查询参数:', params);

            // 参数验证
            if (!params.deviceId) {
                throw new Error('设备ID不能为空');
            }
            if (!params.startTime || !params.endTime) {
                throw new Error('时间范围不能为空');
            }
            if (!params.dataTypes || params.dataTypes.length === 0) {
                throw new Error('数据类型不能为空');
            }

            const response = await API.tenant.device.historyData.post(params);

            if (response && response.status === 1) {
                logger.info('查询成功:', {
                    total: response.data?.total || 0,
                    chartDataLength: response.data?.chartData?.length || 0,
                    tableDataLength: response.data?.tableData?.length || 0
                });
                logger.groupEnd();
                return response.data;
            } else {
                const errorMsg = response?.message || '查询历史数据失败';
                logger.error('API返回错误:', errorMsg);
                ElMessage.error(errorMsg);
                logger.groupEnd();
                return null;
            }
        } catch (error) {
            logger.error('查询历史数据异常:', error);
            ElMessage.error(error.message || '查询历史数据失败');
            logger.groupEnd();
            return null;
        }
    }

    /**
     * 导出设备历史数据
     * @param {HistoryDataExportParams} params 导出参数
     * @returns {Promise<Blob | null>} 导出文件的Blob对象
     */
    async exportHistoryData(params: HistoryDataExportParams): Promise<Blob | null> {
        try {
            logger.group('导出历史数据');
            logger.info('导出参数:', params);

            // 参数验证
            if (!params.deviceId) {
                throw new Error('设备ID不能为空');
            }
            if (!params.startTime || !params.endTime) {
                throw new Error('时间范围不能为空');
            }
            if (!params.dataTypes || params.dataTypes.length === 0) {
                throw new Error('数据类型不能为空');
            }

            const response = await API.tenant.device.historyDataExport.post(params);

            if (response) {
                logger.info('导出成功');
                logger.groupEnd();
                return response;
            } else {
                logger.error('导出失败: 响应为空');
                ElMessage.error('导出失败');
                logger.groupEnd();
                return null;
            }
        } catch (error) {
            logger.error('导出历史数据异常:', error);
            ElMessage.error(error.message || '导出历史数据失败');
            logger.groupEnd();
            return null;
        }
    }

    /**
     * 生成导出文件名
     * @param {FileNameParams} params 参数
     * @returns {string} 文件名
     */
    generateFileName(params: FileNameParams): string {
        const { deviceName, startTime, endTime, dataTypes, layer } = params;

        // 提取日期部分
        const startDate = startTime.split(' ')[0];
        const endDate = endTime.split(' ')[0];

        // 构建文件名
        let fileName = `${deviceName || '设备'}_历史数据`;

        // 添加分层信息
        if (layer !== null && layer !== undefined) {
            fileName += `_第${layer}层`;
        }

        // 添加数据类型信息
        if (dataTypes && dataTypes.length > 0) {
            const typeNames = {
                innerPressure: '内压',
                outerPressure: '外压',
                flow: '流量',
                temperature: '温度',
                valveOpening: '水嘴开度'
            };
            const types = dataTypes.map(type => typeNames[type] || type).join('_');
            fileName += `_${types}`;
        }

        // 添加时间范围
        if (startDate === endDate) {
            fileName += `_${startDate}`;
        } else {
            fileName += `_${startDate}_${endDate}`;
        }

        return `${fileName}.xlsx`;
    }

    /**
     * 下载文件
     * @param {Blob} blob 文件Blob对象
     * @param {string} fileName 文件名
     */
    downloadFile(blob: Blob, fileName: string): void {
        try {
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            logger.info(`文件下载成功: ${fileName}`);
            ElMessage.success('文件下载成功');
        } catch (error) {
            logger.error('文件下载失败:', error);
            ElMessage.error('文件下载失败');
        }
    }

    /**
     * 验证查询参数
     * @param {Object} params 查询参数
     * @returns {Object} 验证结果 { valid: boolean, message: string }
     */
    validateQueryParams(params) {
        if (!params.deviceId) {
            return { valid: false, message: '请选择设备' };
        }

        if (!params.timeRange || params.timeRange.length !== 2) {
            return { valid: false, message: '请选择时间范围' };
        }

        if (!params.dataTypes || params.dataTypes.length === 0) {
            return { valid: false, message: '请至少选择一种数据类型' };
        }

        // 验证时间范围
        const startTime = new Date(params.timeRange[0]);
        const endTime = new Date(params.timeRange[1]);

        if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
            return { valid: false, message: '时间格式不正确' };
        }

        if (startTime >= endTime) {
            return { valid: false, message: '开始时间必须小于结束时间' };
        }

        // 验证时间范围不能超过30天
        const maxDays = 30;
        const diffDays = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
        if (diffDays > maxDays) {
            return { valid: false, message: `时间范围不能超过${maxDays}天` };
        }

        return { valid: true, message: '' };
    }

    /**
     * 格式化查询参数
     * @param {Object} params 原始参数
     * @returns {Object} 格式化后的参数
     */
    formatQueryParams(params) {
        return {
            deviceId: params.deviceId,
            layer: params.layer,
            startTime: params.timeRange[0],
            endTime: params.timeRange[1],
            dataTypes: params.dataTypes,
            interval: params.interval || '10m',
            page: params.page || 1,
            pageSize: params.pageSize || 50
        };
    }

    /**
     * 获取数据类型配置
     * @returns {Object} 数据类型配置
     */
    getDataTypeConfig() {
        return {
            innerPressure: {
                label: '内压',
                unit: 'MPa',
                color: '#5470c6',
                decimals: 3
            },
            outerPressure: {
                label: '外压',
                unit: 'MPa',
                color: '#800080',
                decimals: 3
            },
            flow: {
                label: '流量',
                unit: 'm³/d',
                color: '#32935b',
                decimals: 2
            },
            temperature: {
                label: '温度',
                unit: '℃',
                color: '#ff6565',
                decimals: 1
            },
            valveOpening: {
                label: '水嘴开度',
                unit: '%',
                color: '#036c7a',
                decimals: 1
            }
        };
    }

    /**
     * 获取数据间隔选项
     * @returns {Array} 数据间隔选项
     */
    getIntervalOptions() {
        return [
            { label: '1分钟', value: '1m' },
            { label: '5分钟', value: '5m' },
            { label: '10分钟', value: '10m' },
            { label: '30分钟', value: '30m' },
            { label: '1小时', value: '1h' },
            { label: '6小时', value: '6h' },
            { label: '1天', value: '1d' }
        ];
    }
}

// 创建单例实例
const historyDataService = new HistoryDataService();

export default historyDataService;
