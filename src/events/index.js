/**
 * 事件注册入口文件
 * 集中管理所有事件的注册
 */
import eventManager from '@/utils/eventManager';
import mqttEvents from './mqttEvents';

// 事件系统接口类型
export interface EventSystem {
    initialize: () => boolean;
    isInitialized: () => boolean;
}

// 初始化状态标志
let initialized: boolean = false;

/**
 * 导入并初始化所有事件模块
 * @returns {boolean} 是否是首次初始化
 */
const initializeEvents = (): boolean => {
    if (initialized) {
        console.log('事件系统已经初始化，跳过重复初始化');
        return false;
    }

    // 初始化MQTT相关事件
    mqttEvents.initialize();

    console.log('所有事件已注册完成');
    initialized = true;
    return true;
};

/**
 * 检查事件系统是否已初始化
 * @returns {boolean} 是否已初始化
 */
const isInitialized = (): boolean => {
    return initialized;
};

// 导出事件相关函数
const eventSystem: EventSystem = {
    initialize: initializeEvents,
    isInitialized
};

export default eventSystem;
