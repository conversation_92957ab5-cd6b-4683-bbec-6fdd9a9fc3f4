import type { App } from 'vue';
import config from './config';
import api from './api';
import tool from './utils/tool';
import account from './utils/account';
import http from './utils/request';
import { permission, rolePermission } from './utils/permission';

import scTable from './components/scTable';
import scTableColumn from './components/scTable/column';
import scFilterBar from './components/scFilterBar';
import scUpload from './components/scUpload';
import scUploadMultiple from './components/scUpload/multiple';
import scUploadFile from './components/scUpload/file';
import scFormTable from './components/scFormTable';
import scTableSelect from './components/scTableSelect';
import scPageHeader from './components/scPageHeader';
import scSelect from './components/scSelect';
import scDialog from './components/scDialog';
import scForm from './components/scForm';
import scTitle from './components/scTitle';
import scWaterMark from './components/scWaterMark';
import scQrCode from './components/scQrCode';

import scStatusIndicator from './components/scMini/scStatusIndicator';
import scTrend from './components/scMini/scTrend';

import auth from './directives/auth';
import auths from './directives/auths';
import authsAll from './directives/authsAll';
import role from './directives/role';
import time from './directives/time';
import copy from './directives/copy';
import { vDebounce, vThrottle } from '@/directives/throttleDebounce';
import { debounce, throttle } from '@/utils/throttleDebounce';
import errorHandler from './utils/errorHandler';

import * as elIcons from '@element-plus/icons-vue';
import * as scIcons from './assets/icons';

// 扩展全局属性类型
declare module '@vue/runtime-core' {
    interface ComponentCustomProperties {
        $CONFIG: typeof config;
        $TOOL: typeof tool;
        $ACCOUNT: typeof account;
        $HTTP: typeof http;
        $API: typeof api;
        $AUTH: typeof permission;
        $ROLE: typeof rolePermission;
        $debounce: typeof debounce;
        $throttle: typeof throttle;
    }
}

// 扩展Window类型
declare global {
    interface Window {
        ASYNC_VALIDATOR_NO_WARNING: number;
        isMac: boolean;
    }
}

export default {
    install(app: App) {
        // 挂载全局对象
        app.config.globalProperties.$CONFIG = config;
        app.config.globalProperties.$TOOL = tool;
        app.config.globalProperties.$ACCOUNT = account;
        app.config.globalProperties.$HTTP = http;
        app.config.globalProperties.$API = api;
        app.config.globalProperties.$AUTH = permission;
        app.config.globalProperties.$ROLE = rolePermission;
        app.config.globalProperties.$debounce = debounce;
        app.config.globalProperties.$throttle = throttle;

        // 注册全局组件
        app.component('scTable', scTable);
        app.component('scTableColumn', scTableColumn);
        app.component('scFilterBar', scFilterBar);
        app.component('scUpload', scUpload);
        app.component('scUploadMultiple', scUploadMultiple);
        app.component('scUploadFile', scUploadFile);
        app.component('scFormTable', scFormTable);
        app.component('scTableSelect', scTableSelect);
        app.component('scPageHeader', scPageHeader);
        app.component('scSelect', scSelect);
        app.component('scDialog', scDialog);
        app.component('scForm', scForm);
        app.component('scTitle', scTitle);
        app.component('scWaterMark', scWaterMark);
        app.component('scQrCode', scQrCode);
        app.component('scStatusIndicator', scStatusIndicator);
        app.component('scTrend', scTrend);

        // 注册全局指令
        app.directive('auth', auth);
        app.directive('auths', auths);
        app.directive('auths-all', authsAll);
        app.directive('role', role);
        app.directive('time', time);
        app.directive('copy', copy);
        app.directive('debounce', vDebounce);
        app.directive('throttle', vThrottle);

        // 统一注册el-icon图标
        for (const icon in elIcons) {
            app.component(`ElIcon${icon}`, (elIcons as any)[icon]);
        }

        // 统一注册sc-icon图标
        for (const icon in scIcons) {
            app.component(`ScIcon${icon}`, (scIcons as any)[icon]);
        }

        // 关闭async-validator全局控制台警告
        window.ASYNC_VALIDATOR_NO_WARNING = 1;

        // 是否mac系统判断
        window.isMac = /macintosh|mac os x/i.test(navigator.userAgent);

        // 全局代码错误捕捉
        app.config.errorHandler = errorHandler;
        // 去除控制台的warn 信息
        app.config.warnHandler = () => null;
        // 添加这行以避免一些警告
        (app.config as any).unwrapInjectedRef = true;
    }
};
