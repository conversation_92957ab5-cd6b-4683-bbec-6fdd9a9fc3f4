<template>
    <div class="document-container">
        <div class="document-sidebar">
            <div class="sidebar-header">
                    <!--<h3>文档目录</h3>-->
                    <el-input
                        v-model="searchQuery"
                        placeholder="搜索文档"
                        clearable
                        @input="handleSearch"
                    />
                </div>
                <el-scrollbar style="height: calc(100% - 80px);">
                <el-tree
                    :data="filteredDocTree"
                    :props="defaultProps"
                    @node-click="handleNodeClick"
                    highlight-current
                    default-expand-all
                >
                    <template #default="{ node, data }">
                        <div class="custom-tree-node">
                            <el-icon v-if="data.isFolder"><el-icon-folder /></el-icon>
                            <el-icon v-else-if="data.path && data.path.toLowerCase().endsWith('.md')"><el-icon-memo /></el-icon>
                            <el-icon v-else><el-icon-document /></el-icon>
                            <span>{{ node.label }}</span>
                        </div>
                    </template>
                </el-tree>
            </el-scrollbar>
        </div>
        <div class="document-content">
            <el-scrollbar>
                <div v-if="currentDoc" class="markdown-content">
                    <div v-html="renderedContent" @click.capture="handleAnchorClick"></div>
                </div>
                <div v-else class="empty-content">
                    <el-empty description="请选择左侧文档进行查看" />
                </div>
            </el-scrollbar>
        </div>
    </div>
</template>

<script>
import { marked } from 'marked';
import documentService from './documentService';

export default {
    name: 'DocumentView',
    data() {
        return {
            docTree: [],
            searchQuery: '',
            currentDoc: null,
            defaultProps: {
                children: 'children',
                label: 'label'
            }
        };
    },
    computed: {
        renderedContent() {
            if (!this.currentDoc) return '';
            return marked(this.currentDoc.content || '');
        },
        filteredDocTree() {
            if (!this.searchQuery) return this.docTree;
            const query = this.searchQuery.toLowerCase();
            return this.docTree.map(node => this.filterTree(node, query)).filter(Boolean);
        }
    },
    mounted() {
        marked.setOptions({
            breaks: true
        });
        this.loadDocTree();
    },
    methods: {
        filterTree(node, query) {
            if (node.label.toLowerCase().includes(query)) {
                return node;
            }
            if (node.children) {
                const filteredChildren = node.children
                    .map(child => this.filterTree(child, query))
                    .filter(Boolean);
                if (filteredChildren.length > 0) {
                    return {
                        ...node,
                        children: filteredChildren
                    };
                }
            }
            return null;
        },
        async loadDocTree() {
            try {
                this.docTree = await documentService.getDocTree();
            } catch (error) {
                console.error('加载文档目录失败:', error);
            }
        },
        async handleNodeClick(data) {
            if (data.isFolder) return;

            try {
                const content = await documentService.getDocContent(data.path);
                this.currentDoc = {
                    ...data,
                    content: content
                };
                const scrollbar = document.querySelector('.document-content .el-scrollbar__wrap');
                if (scrollbar) {
                    scrollbar.scrollTop = 0;
                }
            } catch (error) {
                console.error('加载文档内容失败:', error);
            }
        },
        handleAnchorClick(event) {
            const target = event.target;
            if (target.tagName === 'A' && target.getAttribute('href')?.startsWith('#')) {
                event.preventDefault();
                const id = target.getAttribute('href').slice(1);
                const element = document.getElementById(id);
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth' });
                }
            }
        },
        handleSearch(value) {
            this.searchQuery = value;
        }
    }
};
</script>

<style lang="scss" scoped>
.document-container {
    display: flex;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

.document-sidebar {
    width: 280px;
    border-right: 1px solid #e6e6e6;
    height: 100%;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .sidebar-header {
        padding: 20px;
        border-bottom: 1px solid #e6e6e6;
        h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }
    }

    :deep(.el-tree) {
        padding: 12px;

        .el-tree-node__content {
            height: 36px;
            border-radius: 4px;
            transition: all 0.3s;

            &:hover {
                background-color: #ecf5ff;
            }

            &.is-current {
                background-color: #ecf5ff;
                color: #409eff;
            }
        }
    }

    .custom-tree-node {
        display: flex;
        align-items: center;
        font-size: 14px;

        .el-icon {
            margin-right: 8px;
            font-size: 16px;
            color: #909399;
        }

        span {
            transition: color 0.3s;
        }
    }
}

.document-content {
    flex: 1;
    height: 100%;
    overflow: hidden;
    padding: 20px;
    box-sizing: border-box;

    .markdown-content {
        padding: 20px;
        max-width: 900px;
        margin: 0 auto;
        color: #24292e;

        :deep(h1) {
            font-size: 32px;
            border-bottom: 2px solid #eaecef;
            padding-bottom: 0.5em;
            margin-top: 32px;
            margin-bottom: 20px;
            color: #1a1a1a;
        }

        :deep(h2) {
            font-size: 24px;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.4em;
            margin-top: 28px;
            margin-bottom: 18px;
            color: #1a1a1a;
        }

        :deep(h3) {
            font-size: 20px;
            margin-top: 24px;
            margin-bottom: 16px;
            color: #1a1a1a;
        }

        :deep(h4) {
            font-size: 16px;
            margin-top: 24px;
            margin-bottom: 16px;
            color: #1a1a1a;
        }

        :deep(h5) {
            font-size: 14px;
            margin-top: 24px;
            margin-bottom: 16px;
            color: #1a1a1a;
        }

        :deep(h6) {
            font-size: 12px;
            margin-top: 24px;
            margin-bottom: 16px;
            color: #1a1a1a;
        }

        :deep(p) {
            line-height: 1.8;
            margin-bottom: 18px;
            font-size: 15px;
        }

        :deep(code) {
            border-radius: 4px;
            background-color: #eee;
            margin: 0 0.2em;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            padding: 0.2em 0.4em;
            color: #555;
            line-height: 1.5;
        }

        :deep(pre) {
            background-color: #fafafa;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            line-height: 1.6;
            overflow: auto;
            padding: 20px;
            margin: 10px 0 20px 0;
            border: 1px solid #eaecef;

            code {
                color: #666;
                background-color: transparent;
                margin: 0;
                padding: 0;
            }
        }

        :deep(table) {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
            border: 1px solid #eaecef;
            border-radius: 4px;
            overflow: hidden;
        }

        :deep(th), :deep(td) {
            border: 1px solid #eaecef;
            padding: 10px 16px;
        }

        :deep(th) {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #1a1a1a;
        }

        :deep(tr:hover) {
            background-color: #f8f9fa;
        }

        :deep(blockquote) {
            border-left: 4px solid #409eff;
            color: #5c6b77;
            margin: 0 0 20px;
            padding: 12px 20px;
            background-color: #f8f9fa;
            border-radius: 0 4px 4px 0;

            p {
                margin: 0;
            }
        }

        :deep(ul), :deep(ol) {
            padding-left: 2em;
            margin-bottom: 20px;
        }

        :deep(li) {
            margin-bottom: 0.5em;
            line-height: 1.6;
        }

        :deep(img) {
            max-width: 100%;
            border-radius: 4px;
            margin: 10px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        :deep(a) {
            color: #409eff;
            text-decoration: none;
            transition: color 0.3s ease;

            &:hover {
                color: #66b1ff;
                text-decoration: underline;
            }
        }
    }

    .empty-content {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
    }
}
</style>
