import API from '@/api';

/**
 * 文档服务，用于处理文档的加载和解析
 */
const documentService = {
    /**
     * 获取文档目录结构
     * @returns {Promise<Array>} 文档目录树
     */
    async getDocTree() {
        try {
            const response = await API.common.docs.tree.get();
            if (response.code === 0) {
                return response.data;
            }
            return [];
        } catch (error) {
            console.error('获取文档目录失败:', error);
            return [];
        }
    },

    /**
     * 获取文档内容
     * @param {String} path 文档路径
     * @returns {Promise<String>} 文档内容
     */
    async getDocContent(path) {
        try {
            const response = await API.common.docs.content.get({ path });
            if (response.code === 0) {
                return response.data;
            }
            return '';
        } catch (error) {
            console.error('获取文档内容失败:', error);
            return '';
        }
    }
};

export default documentService;
