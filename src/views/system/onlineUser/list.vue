<template>
    <el-container>
        <el-header>
            <div class="left-panel">
                <el-button icon="el-icon-refresh" plain type="success" @click="searchHandle">刷新</el-button>
                <el-button
                    v-auths="['system.onlineUser.batchKickOut']"
                    :disabled="!selection.length"
                    icon="el-icon-promotion"
                    plain
                    type="warning"
                    @click="batchKickOut"
                    >批量踢出</el-button
                >
                <el-button
                    v-auths="['system.onlineUser.batchBlock']"
                    :disabled="!selection.length"
                    icon="el-icon-warn-triangle-filled"
                    plain
                    type="danger"
                    @click="batchBlock"
                    >批量拉黑</el-button
                >
                <el-button
                    v-auths="['system.onlineUser.blackList']"
                    icon="el-icon-warning"
                    plain
                    type="info"
                    @click="showBlacklist"
                    >黑名单</el-button
                >
                <!--<span>当前活跃人数：{{ total }}</span>-->
            </div>
            <div class="right-panel">
                <div class="right-panel-search">
                    <el-input
                        style="min-width: 225px"
                        v-model="searchObj.keyword"
                        clearable
                        placeholder="指纹ID / 用户名 / 真实名称"
                        @keyup.enter="searchHandle"
                        @clear="searchHandle"
                    ></el-input>
                    <el-button icon="el-icon-search" type="primary" color="#1C409A" @click="searchHandle"
                        >搜索</el-button
                    >
                </div>
            </div>
        </el-header>
        <el-main class="p0">
            <scTable
                ref="onlineUserDataTable"
                :apiObj="onlineUserDataObj"
                remoteFilter
                remoteSort
                row-key="id"
                stripe
                :row-class-name="tableRowClassName"
                :selectable="checkSelectable"
                @selection-change="selectionChange"
                @data-change="handleDataChange"
                class="custom-list-table"
            >
                <el-table-column type="selection" width="50" :selectable="checkSelectable"></el-table-column>
                <el-table-column
                    label="指纹ID"
                    prop="finger_id"
                    sortable="custom"
                    width="150"
                    fixed
                    show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                    label="用户名"
                    prop="username"
                    width="150"
                    fixed
                    show-overflow-tooltip
                ></el-table-column>
                <el-table-column label="真实名称" prop="realname" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column label="终端ID" prop="client_id" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column label="操作系统" prop="os" width="150"></el-table-column>
                <el-table-column label="浏览器" prop="browser" width="180"></el-table-column>
                <el-table-column label="登录模式" prop="login_mode" width="150"></el-table-column>
                <el-table-column label="登录方式" prop="login_type" width="150"></el-table-column>
                <el-table-column label="登录次数" prop="login_times" width="100"></el-table-column>
                <el-table-column label="登录IP" prop="login_ip" width="150"></el-table-column>
                <el-table-column label="登录时间" prop="login_at" width="200"></el-table-column>
                <el-table-column label="上次登录IP" prop="last_login_ip" width="150"></el-table-column>
                <el-table-column label="上次登录时间" prop="last_login_at" width="200"></el-table-column>
                <el-table-column label="最后活跃时间" prop="last_active_time" width="200"></el-table-column>
                <el-table-column align="center" fixed="right" label="操作" width="170">
                    <template #default="scope">
                        <el-dropdown>
                            <el-button
                                icon="el-icon-more"
                                size="small"
                                color="#28a745"
                                type="success"
                                style="margin: 3px 6px 0 0"
                                >查看</el-button
                            >
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click="user_view(scope.row, scope.$index)"
                                        >用户详情</el-dropdown-item
                                    >
                                    <el-dropdown-item @click="log_view(scope.row, scope.$index)"
                                        >操作日志</el-dropdown-item
                                    >
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                        <el-tooltip
                            content="无法对当前设备进行操作"
                            placement="top"
                            :disabled="scope.row.finger_id !== currentFingerId"
                        >
                            <el-button
                                v-auths="['system.onlineUser.kickOut']"
                                size="small"
                                type="warning"
                                @click="kickOutUser(scope.row)"
                                :disabled="scope.row.finger_id === currentFingerId"
                                >踢出</el-button
                            >
                        </el-tooltip>
                        <el-tooltip
                            content="无法对当前设备进行操作"
                            placement="top"
                            :disabled="scope.row.finger_id !== currentFingerId"
                        >
                            <el-button
                                v-auths="['system.onlineUser.block']"
                                size="small"
                                type="danger"
                                @click="blockUser(scope.row)"
                                :disabled="scope.row.finger_id === currentFingerId"
                                >拉黑</el-button
                            >
                        </el-tooltip>
                    </template>
                </el-table-column>
            </scTable>
        </el-main>
    </el-container>

    <!--查看对话框-->
    <el-dialog
        ref="infoDialog"
        v-model="dialog.info"
        title="用户详情"
        width="600"
        :destroy-on-close="true"
        :draggable="true"
        :close-on-click-modal="false"
    >
        <el-descriptions v-if="userDetail" :column="1" :border="true">
            <el-descriptions-item label-align="right" label="用户ID">{{ userDetail.id }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="用户名">{{ userDetail.username }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="真实名称">{{ userDetail.realname }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="手机">{{ userDetail.phone }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="邮箱">{{ userDetail.email }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="是否超管">{{ userDetail.is_super }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="锁屏状态">{{
                userDetail.lockscreen
            }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="强制修改密码">{{
                userDetail.force_change_password
            }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="创建时间">{{ userDetail.create_at }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="角色名称">{{
                userDetail.roles_name
            }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="登录类型">{{
                userDetail.login_type
            }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="登录时间">{{ userDetail.login_at }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="登录IP">{{ userDetail.login_ip }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="上次登录时间">{{
                userDetail.last_login_at
            }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="上次登录IP">{{
                userDetail.last_login_ip
            }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="登录次数">{{
                userDetail.login_times
            }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="登录模式">{{
                userDetail.login_mode
            }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="终端ID">{{ userDetail.client_id }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="浏览器">{{ userDetail.browser }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="操作系统">{{ userDetail.os }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="指纹ID">{{ userDetail.finger_id }}</el-descriptions-item>
            <el-descriptions-item label-align="right" label="最后活跃时间">{{
                userDetail.last_active_time
            }}</el-descriptions-item>
        </el-descriptions>
    </el-dialog>

    <!--操作日志对话框-->
    <el-dialog
        v-model="dialog.logList"
        title="用户操作日志"
        width="1000"
        :destroy-on-close="true"
        :draggable="true"
        :close-on-click-modal="false"
    >
        <user-log-list ref="logListRef"></user-log-list>
    </el-dialog>

    <!-- 黑名单对话框 -->
    <blacklist-dialog ref="blacklistDialog"></blacklist-dialog>
</template>

<script>
import ScTable from '@/components/scTable/index.vue';
import userLogList from '@/views/profile/logs/list';
import BlacklistDialog from './blacklist-dialog.vue';
import fingerprint from '@/utils/fingerprint';

export default {
    name: 'system.onlineUser.list',
    components: {
        userLogList,
        ScTable,
        BlacklistDialog
    },
    data() {
        return {
            dialog: {
                info: false,
                logList: false
            },
            userDetail: null, // 新增存储详情数据
            onlineUserDataObj: this.$API.system.onlineUser.list,
            selection: [],
            searchObj: {
                keyword: null
            },
            total: 0, // 活跃人数计数
            currentFingerId: '' // 添加当前指纹ID存储
        };
    },
    async created() {
        // 获取当前设备指纹
        try {
            this.currentFingerId = await fingerprint.getStableFingerprint();
        } catch (error) {
            console.error('获取设备指纹失败:', error);
        }
    },
    methods: {
        /**
         * 检查行是否可选
         * @param {Object} row
         * @returns {boolean}
         */
        checkSelectable(row) {
            return row.finger_id !== this.currentFingerId;
        },

        /**
         * 用户查看
         * @param row
         */
        user_view(row) {
            this.userDetail = row; // 直接使用列表数据中的详情
            this.dialog.info = true;
        },
        /**
         * 用户操作日志
         * @param row
         */
        log_view(row) {
            this.dialog.logList = true;
            this.$nextTick(() => {
                this.$refs.logListRef.getLogList(row);
            });
        },
        /**
         * 批量删除
         * @returns {Promise<void>}
         */
        async batch_delete() {
            this.$confirm(`确定删除选中的 ${this.selection.length} 项在线用户组吗？`, '提示', {
                type: 'warning'
            })
                .then(async () => {
                    const loading = this.$loading();

                    let ids = this.selection.map(item => {
                        return item.id;
                    });

                    const reqData = { id: ids };

                    const res = await this.$API.onlineUser.delete.post(reqData);

                    if (res.status === 1) {
                        this.selection.forEach(item => {
                            this.$refs.onlineUserDataTable.tableData.forEach((itemI, indexI) => {
                                if (item.id === itemI.id) {
                                    this.$refs.onlineUserDataTable.tableData.splice(indexI, 1);
                                }
                            });
                        });

                        this.$message.success(res.message);
                    } else {
                        this.$message.error(res.message);
                    }

                    loading.close();
                })
                .catch(() => {});
        },
        /**
         * 表格选择后回调事件
         * @param selection
         */
        selectionChange(selection) {
            this.selection = selection || []; // 直接使用selection数组，移除多余的过滤
        },
        /**
         * 搜索回调
         */
        searchHandle() {
            this.$refs.onlineUserDataTable.upData(this.searchObj);
        },
        /**
         * 表格数据变化回调
         * @param res
         */
        handleDataChange(res) {
            this.total = res?.data?.total || 0;
        },
        /**
         * 踢出单个用户
         * @param row
         */
        async kickOutUser(row) {
            try {
                await this.$confirm('确定要踢出该用户吗？', '提示', {
                    type: 'warning'
                });

                const loading = this.$loading();
                try {
                    const res = await this.$API.system.onlineUser.kickOut.post({
                        item: `${row.id}:${row.finger_id}`
                    });
                    if (res.status === 1) {
                        this.$message.success(res.message);
                        this.searchHandle();
                    } else {
                        this.$message.error(res.message);
                    }
                } finally {
                    loading.close();
                }
            } catch (e) {
                console.info('踢出操作失败:', e);
            }
        },

        /**
         * 拉黑单个用户
         * @param row
         */
        async blockUser(row) {
            try {
                await this.$confirm('确定要拉黑该用户吗？', '提示', {
                    type: 'warning'
                });

                const loading = this.$loading();
                try {
                    const res = await this.$API.system.onlineUser.block.post({
                        item: `${row.id}:${row.finger_id}`
                    });
                    if (res.status === 1) {
                        this.$message.success(res.message);
                        this.searchHandle();
                    } else {
                        this.$message.error(res.message);
                    }
                } finally {
                    loading.close();
                }
            } catch (e) {
                console.info('拉黑操作失败:', e);
            }
        },

        /**
         * 批量踢出
         */
        async batchKickOut() {
            if (this.selection.length === 0) return;

            // 过滤掉当前设备
            const filteredSelection = this.selection.filter(row => row.finger_id !== this.currentFingerId);
            if (filteredSelection.length === 0) {
                this.$message.warning('无可操作的用户');
                return;
            }

            try {
                await this.$confirm(`确定要踢出选中的 ${filteredSelection.length} 个用户吗？`, '提示', {
                    type: 'warning'
                });

                const loading = this.$loading();
                try {
                    const items = filteredSelection.map(row => `${row.id}:${row.finger_id}`);
                    const res = await this.$API.system.onlineUser.batchKickOut.post({ items });
                    if (res.status === 1) {
                        this.$message.success(res.message);
                        this.searchHandle();
                    } else {
                        this.$message.error(res.message);
                    }
                } finally {
                    loading.close();
                }
            } catch (e) {
                console.info('踢出操作失败:', e);
            }
        },

        /**
         * 批量拉黑
         */
        async batchBlock() {
            if (this.selection.length === 0) return;

            // 过滤掉当前设备
            const filteredSelection = this.selection.filter(row => row.finger_id !== this.currentFingerId);
            if (filteredSelection.length === 0) {
                this.$message.warning('无可操作的用户');
                return;
            }

            try {
                await this.$confirm(`确定要拉黑选中的 ${filteredSelection.length} 个用户吗？`, '提示', {
                    type: 'warning'
                });

                const loading = this.$loading();
                try {
                    const items = filteredSelection.map(row => `${row.id}:${row.finger_id}`);
                    const res = await this.$API.system.onlineUser.batchBlock.post({ items });
                    if (res.status === 1) {
                        this.$message.success(res.message);
                        this.searchHandle();
                    } else {
                        this.$message.error(res.message);
                    }
                } finally {
                    loading.close();
                }
            } catch (e) {
                console.info('拉黑操作失败:', e);
            }
        },

        /**
         * 显示黑名单列表
         */
        showBlacklist() {
            this.$refs.blacklistDialog.show();
        },

        /**
         * 设置表格行的类名
         * @param {Object} row
         * @returns {string}
         */
        tableRowClassName({ row }) {
            if (row.finger_id === this.currentFingerId) {
                return 'current-device-row';
            }
            return '';
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(.current-device-row),
:deep(.current-device-row td),
:deep(.el-table--striped .current-device-row),
:deep(.el-table--striped .current-device-row.el-table__row--striped td) {
    background-color: var(--el-color-primary-light-9) !important;
}
</style>
