<template>
    <el-dialog
        title="黑名单列表"
        v-model="dialogVisible"
        width="900px"
        :destroy-on-close="true"
        :close-on-click-modal="false"
    >
        <scTable
            ref="blacklistTable"
            :apiObj="blacklistDataObj"
            remoteFilter
            remoteSort
            row-key="user_id"
            stripe
            class="custom-list-table"
            style="min-height: 300px"
        >
            <el-table-column prop="username" label="用户名" width="150" show-overflow-tooltip />
            <el-table-column prop="realname" label="真实姓名" width="150" show-overflow-tooltip />
            <el-table-column prop="login_ip" label="登录IP" width="150" />
            <el-table-column prop="finger_id" label="指纹ID" width="180" show-overflow-tooltip />
            <el-table-column prop="block_time" label="拉黑时间" width="180" />
            <el-table-column prop="block_period" label="封禁时长" width="120" />
            <el-table-column label="操作" width="120" align="center" fixed="right">
                <template #default="{ row }">
                    <el-button type="primary" link @click="handleRemove(row)"> 移除黑名单 </el-button>
                </template>
            </el-table-column>
        </scTable>
    </el-dialog>
</template>

<script>
import ScTable from '@/components/scTable/index.vue';

export default {
    name: 'BlacklistDialog',
    components: {
        ScTable
    },
    data() {
        return {
            dialogVisible: false,
            blacklistDataObj: this.$API.system.onlineUser.blackList
        };
    },
    methods: {
        show() {
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs.blacklistTable?.upData();
            });
        },
        async handleRemove(row) {
            try {
                await this.$confirm('确定要将该用户从黑名单中移除吗？', '提示', {
                    type: 'warning'
                });

                const res = await this.$API.system.onlineUser.removeBlock.post({
                    item: `${row.user_id}:${row.finger_id}`
                });

                if (res.status === 1) {
                    this.$message.success('移除成功');
                    this.$refs.blacklistTable?.upData();
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                console.error(error);
            }
        }
    }
};
</script>
