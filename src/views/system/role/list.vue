<template>
    <el-container>
        <el-header>
            <div class="left-panel">
                <el-button
                    v-auths="['system.role.add']"
                    icon="el-icon-plus"
                    type="primary"
                    @click="role_add"
                    color="#1C409A"
                    >添加角色</el-button
                >
                <el-button
                    v-auths="['system.role.delete']"
                    :disabled="selection.length === 0"
                    icon="el-icon-delete"
                    plain
                    type="danger"
                    @click="batch_delete"
                    >删除</el-button
                >
                <!--<el-button :disabled="selection.length != 1" plain type="primary" @click="permission"
                    >权限设置</el-button>-->
            </div>
            <div class="right-panel">
                <div class="right-panel-search">
                    <el-input
                        style="min-width: 225px"
                        v-model="searchObj.keyword"
                        clearable
                        placeholder="角色名称 / 角色标识 / 角色描述"
                        @keyup.enter="searchHandle"
                        @clear="searchHandle"
                    ></el-input>
                    <el-button icon="el-icon-search" type="primary" color="#1C409A" @click="searchHandle"></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="p0">
            <scTable
                ref="roleDataTable"
                :apiObj="roleDataObj"
                :cell-class-name="tableCellClassName"
                remoteFilter
                remoteSort
                row-key="id"
                stripe
                @selection-change="selectionChange"
                @row-dblclick="rowDblclickHandle"
                class="custom-list-table"
            >
                <el-table-column type="selection" width="50">
                    <template #default="scope">
                        <span v-if="scope.row.is_system === 1"></span>
                    </template>
                </el-table-column>
                <el-table-column label="ID" prop="id" sortable="custom" width="80" fixed></el-table-column>
                <el-table-column label="角色名称" prop="name" width="150" fixed show-overflow-tooltip></el-table-column>
                <el-table-column label="角色标识" prop="alias" width="160" show-overflow-tooltip></el-table-column>
                <el-table-column label="是否系统" prop="is_system" width="100">
                    <template #default="scope">
                        <el-tag v-if="scope.row.is_system === 1" type="success">是</el-tag>
                        <el-tag v-if="scope.row.is_system === 0" type="danger">否</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="用户数" prop="user_counts" align="center" width="80"></el-table-column>
                <el-table-column label="排序" prop="sort" sortable="custom" width="80">
                    <template #default="scope">
                        <!--v-if去判断双击的是不是当前单元格-->
                        <!--回车才能提交，blur还原原来值-->
                        <el-input
                            v-if="scope.row.index + ',' + scope.column.index === columnEditObj.currentCellPos"
                            v-model="scope.row.sort"
                            :ref="scope.row.index + ',' + scope.column.index"
                            placeholder="请输入排序号"
                            size="small"
                            style="width: 60px"
                            @keyup.enter="hideCellEditInput(true)"
                            @blur="hideCellEditInput(false)"
                        >
                        </el-input>
                        <span v-else>
                            <el-tooltip effect="dark" placement="right">
                                <template #content>
                                    <label>双击编辑</label>
                                </template>
                                <label>{{ scope.row.sort }}</label>
                            </el-tooltip>
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    :filters="[
                        { text: '已删除', value: '-1' },
                        { text: '已冻结', value: '0' },
                        { text: '正常', value: '1' }
                    ]"
                    column-key="filter[status]"
                    align="center"
                    label="状态"
                    prop="status"
                    width="80"
                >
                    <template #default="scope">
                        <el-tooltip effect="dark" placement="right">
                            <template #content>
                                <span v-if="scope.row.status === -1">已删除</span>
                                <span v-if="scope.row.status === 0">已冻结</span>
                                <span v-if="scope.row.status === 1">正常</span>
                            </template>
                            <el-switch
                                v-auths="['system.role.status']"
                                size="small"
                                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #bfbfbf"
                                v-model="scope.row.status"
                                :loading="scope.row.loading || false"
                                :disabled="scope.row.is_system === 1 || scope.row.status < 0"
                                :active-value="1"
                                :inactive-value="0"
                                @change="switchRoleStatus($event, scope.row)"
                            />
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="create_at" sortable="custom" width="180"></el-table-column>
                <el-table-column
                    label="角色描述"
                    show-overflow-tooltip
                    min-width="150"
                    prop="description"
                ></el-table-column>
                <el-table-column align="center" fixed="right" label="操作" width="220">
                    <template #default="scope">
                        <el-button
                            v-auths="['system.role.access']"
                            size="small"
                            type="primary"
                            :disabled="scope.row.status < 0"
                            @click="role_permission(scope.row, scope.$index)"
                            color="#6a2ea1"
                            >权限</el-button
                        >
                        <el-button
                            size="small"
                            type="success"
                            @click="role_view(scope.row, scope.$index)"
                            color="#28a745"
                            >查看</el-button
                        >
                        <el-button
                            v-auths="['system.role.edit']"
                            size="small"
                            type="primary"
                            :disabled="scope.row.status < 0"
                            @click="role_edit(scope.row, scope.$index)"
                            color="#1C409A"
                            >编辑</el-button
                        >
                        <el-popconfirm title="确定删除该角色吗？" @confirm="role_delete(scope.row, scope.$index)">
                            <template #reference>
                                <el-button
                                    v-auths="['system.role.delete']"
                                    size="small"
                                    type="danger"
                                    :disabled="scope.row.status < 0 || scope.row.is_system === 1"
                                    >删除</el-button
                                >
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </scTable>
        </el-main>
    </el-container>

    <!--添加编辑对话框-->
    <save-dialog
        v-if="dialog.save"
        ref="saveDialog"
        @saveDialogClosedEmit="saveDialogClosedHandle"
        @saveSuccessEmit="handleSaveSuccess"
    ></save-dialog>

    <!--权限设置对话框-->
    <permission-dialog
        v-if="dialog.permission"
        ref="permissionDialog"
        @saveDialogClosedEmit="dialog.permission = false"
        @saveSuccessEmit="handleSaveSuccess"
    ></permission-dialog>
</template>

<script>
import saveDialog from './save';
import permissionDialog from './permission';
import tableConfig from '@/config/table.js';

export default {
    name: 'system.role.list',
    components: {
        saveDialog,
        permissionDialog
    },
    data() {
        return {
            // 行编辑对象
            columnEditObj: {
                // 要编辑的当前行原始数据
                currentColumnOld: {},
                // 要编辑的当前行原始数据
                currentColumnNew: {},
                // 要编辑的当前单元格坐标
                currentCellPos: null
            },
            dialog: {
                save: false,
                permission: false
            },
            roleDataObj: this.$API.system.role.list,
            selection: [],
            searchObj: {
                keyword: null
            }
        };
    },
    methods: {
        /**
         * 添加
         */
        role_add() {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show();
            });
        },
        /**
         * 角色编辑
         * @param row
         */
        role_edit(row) {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show('edit').setData(row);
            });
        },
        /**
         * 角色查看
         * @param row
         */
        role_view(row) {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show('show').setData(row);
            });
        },
        /**
         * 权限设置
         */
        role_permission(row) {
            this.dialog.permission = true;
            this.$nextTick(() => {
                this.$refs.permissionDialog.show().setData(row);
            });
        },
        /**
         * 角色删除
         * @param row
         * @returns {Promise<void>}
         */
        async role_delete(row) {
            const reqData = { id: row.id };
            const res = await this.$API.system.role.delete.post(reqData);
            if (res.status === 1) {
                this.$refs.roleDataTable.refresh();
                this.$message.success('删除成功');
            } else {
                await this.$alert(res.message, '提示', { type: 'error' });
            }
        },
        /**
         * 批量删除
         * @returns {Promise<void>}
         */
        async batch_delete() {
            this.$confirm(`确定删除选中的 ${this.selection.length} 项角色组吗？`, '提示', {
                type: 'warning'
            })
                .then(async () => {
                    const loading = this.$loading();

                    let ids = this.selection.map(item => {
                        return item.id;
                    });

                    const reqData = { id: ids };

                    const res = await this.$API.system.role.delete.post(reqData);

                    if (res.code === tableConfig.successCode) {
                        this.selection.forEach(item => {
                            this.$refs.roleDataTable.tableData.forEach((itemI, indexI) => {
                                if (item.id === itemI.id) {
                                    this.$refs.roleDataTable.tableData.splice(indexI, 1);
                                }
                            });
                        });

                        this.$message.success(res.message);
                    } else {
                        this.$message.error(res.message);
                    }

                    loading.close();
                })
                .catch(() => {});
        },
        /**
         * 表格选择后回调事件
         * @param selection
         */
        selectionChange(selection) {
            let _selection = [];
            selection.forEach(item => {
                if (item.status >= 0 && item.is_system === 0) {
                    _selection.push(item);
                }
            });
            this.selection = _selection;
        },
        /**
         * 表格内开关
         * @param val
         * @param row
         */
        switchRoleStatus(val, row) {
            row.loading = true;

            this.$confirm(`确定要${row.status === 1 ? '启用' : '禁用'}该角色组【${row.name}】吗？`, '提示', {
                type: 'warning'
            })
                .then(async () => {
                    const res = await this.$API.system.role.status.post({
                        id: row.id,
                        status: row.status
                    });

                    row.loading = false;

                    if (res.status === 1) {
                        this.$message.success(res.message);
                    } else {
                        // 修改失败返回之前的状态
                        row.status = row.status === 1 ? 0 : 1;

                        this.$message.error(res.message);
                    }
                })
                .catch(() => {
                    row.loading = false;

                    // 修改失败返回之前的状态
                    row.status = row.status === 1 ? 0 : 1;
                });
        },
        /**
         * 搜索回调
         */
        searchHandle() {
            this.$refs.roleDataTable.upData(this.searchObj);
        },
        /**
         * 根据ID获取树结构
         * @param id
         * @returns {null}
         */
        filterTree(id) {
            let target = null;

            function filter(tree) {
                tree.forEach(item => {
                    if (item.id === id) {
                        target = item;
                    }
                    if (item.children) {
                        filter(item.children);
                    }
                });
            }

            filter(this.$refs.roleDataTable.tableData);
            return target;
        },
        /**
         * 保存角色对话框关闭后回调
         */
        saveDialogClosedHandle() {
            this.dialog.save = false;
        },
        /**
         * 本地更新数据
         * @param data
         * @param mode
         */
        handleSaveSuccess(data, mode) {
            if (mode === 'add') {
                this.$refs.roleDataTable.refresh();
            } else if (mode === 'edit') {
                this.$refs.roleDataTable.refresh();
            } else {
                this.$refs.roleDataTable.refresh();
            }
        },
        /**
         * 给单元格绑定横向和竖向的index，这样就能确定是哪一个单元格
         * @param row
         * @param column
         * @param rowIndex
         * @param columnIndex
         */
        tableCellClassName({ row, column, rowIndex, columnIndex }) {
            row.index = rowIndex;
            column.index = columnIndex;
        },
        /**
         * 表格行编辑回调事件
         * @param row
         * @param column
         */
        rowDblclickHandle(row, column) {
            // 单元格是sort，且未编辑时可编辑
            if (column.property === 'sort' && JSON.stringify(this.columnEditObj.currentColumnOld) === '{}') {
                this.columnEditObj.currentColumnNew = row;
                // 拷贝一个当前行的原始数据对象
                this.columnEditObj.currentColumnOld = Object.assign({}, row);
                this.columnEditObj.currentCellPos = row.index + ',' + column.index;
            } else {
                return false;
            }
        },
        /**
         * 当input失去焦点的时候，隐藏input
         */
        async hideCellEditInput(submit) {
            // 记录原来的值
            const _old_sort = this.columnEditObj.currentColumnOld.sort;

            if (submit === true) {
                const _sort = parseInt(this.columnEditObj.currentColumnNew.sort);

                const res = await this.$API.system.role.sort.post({
                    id: this.columnEditObj.currentColumnOld.id,
                    sort: _sort
                });

                if (res.status === 1) {
                    this.$message.success(res.message);

                    // 设置成新值
                    this.columnEditObj.currentColumnNew.sort = _sort;

                    // 切换后刷新表格
                    this.$refs.roleDataTable.refresh();
                } else {
                    // 修改失败返回之前的值
                    this.columnEditObj.currentColumnNew.sort = _old_sort;

                    this.$message.error(res.message);
                }
            } else {
                // 还原成原来的值
                this.columnEditObj.currentColumnNew.sort = _old_sort;
            }

            // 清空编辑的数据对象
            this.columnEditObj.currentCellPos = null;
            this.columnEditObj.currentColumnNew = {};
            this.columnEditObj.currentColumnOld = {};
        }
    }
};
</script>

<style lang="scss" scoped></style>
