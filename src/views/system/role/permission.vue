<template>
    <el-dialog
        v-model="visibleDialog"
        :width="600"
        :destroy-on-close="true"
        :draggable="true"
        :close-on-click-modal="false"
        :title="'[' + role_data.name + ']' + '角色权限设置'"
        @saveDialogClosedEmit="$emit('saveDialogClosedEmit')"
    >
        <!--<el-alert
            style="margin-bottom: 20px"
            type="success"
            :show-icon="true"
            :closable="false"
            title="根据角色配置,可让不同角色访问不同的控制台视图,参数值在登录成功后返回 dashboard:{type}"
        ></el-alert>-->
        <el-tabs tab-position="top">
            <el-tab-pane label="菜单权限">
                <div class="treeMain">
                    <el-tree
                        ref="menuRef"
                        node-key="id"
                        :data="node_auth.list"
                        :props="node_auth.props"
                        :show-checkbox="true"
                        :check-strictly="false"
                        :default-expand-all="true"
                        :highlight-current="true"
                        :expand-on-click-node="false"
                    >
                        <template #default="{ node, data }">
                            <span class="el-tree-node__label">
                                <div class="label">
                                    <el-icon><component :is="data.icon || 'el-icon-document'" /></el-icon>
                                    <span>{{ node.label }}</span>
                                </div>
                            </span>
                        </template>
                    </el-tree>
                </div>
            </el-tab-pane>
            <el-tab-pane label="数据权限">
                <el-form label-position="left" label-width="100px">
                    <el-form-item label="规则类型">
                        <el-select v-model="data_auth.visible_access" placeholder="请选择">
                            <el-option label="全部可见" value="0"></el-option>
                            <el-option label="本人可见" value="1"></el-option>
                            <el-option label="所在部门可见" value="2"></el-option>
                            <el-option label="所在部门及子级可见" value="3"></el-option>
                            <el-option label="选择的部门可见" value="4"></el-option>
                            <el-option label="自定义" value="5"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="data_auth.visible_access === '4'" label="选择部门">
                        <div class="treeMain" style="width: 100%">
                            <el-tree
                                ref="deptRef"
                                node-key="id"
                                :data="data_auth.list"
                                :props="data_auth.props"
                                :show-checkbox="true"
                                :check-strictly="true"
                                :default-expand-all="true"
                                :highlight-current="true"
                                :expand-on-click-node="false"
                            ></el-tree>
                        </div>
                    </el-form-item>
                    <el-form-item v-show="data_auth.visible_access === '5'" label="规则值">
                        <el-input
                            v-model="data_auth.visible_rule"
                            :rows="6"
                            clearable
                            placeholder="请输入自定义规则代码"
                            type="textarea"
                        ></el-input>
                    </el-form-item>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="控制台">
                <el-form label-position="left" label-width="100px">
                    <el-form-item label="控制台视图">
                        <el-select v-model="dash_auth.checked" placeholder="请选择">
                            <el-option
                                v-for="item in dash_auth.options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                                <span style="float: left">{{ item.label }}</span>
                                <span style="float: right; color: #8492a6; font-size: 12px">{{ item.value }}</span>
                            </el-option>
                        </el-select>
                        <div class="el-form-item-msg">用于控制角色登录后控制台的视图</div>
                    </el-form-item>
                </el-form>
            </el-tab-pane>
        </el-tabs>
        <template #footer>
            <el-button type="warning" icon="el-icon-warning-filled" @click="visibleDialog = false">取 消</el-button>
            <el-button
                v-auths="['system.role.access']"
                :loading="isSaving"
                type="primary"
                icon="el-icon-circle-check-filled"
                color="#1C409A"
                @click="submit()"
                >保 存</el-button
            >
        </template>
    </el-dialog>
</template>

<script>
export default {
    emits: ['saveSuccessEmit', 'saveDialogClosedEmit'],
    data() {
        return {
            visibleDialog: false,
            isSaving: false,
            // 角色组数据
            role_data: {},
            // 菜单权限
            node_auth: {
                list: [],
                checked: [],
                props: {
                    label: data => {
                        return data.title;
                    }
                }
            },
            // 数据权限
            data_auth: {
                visible_access: '0',
                list: [],
                visible_rule: '',
                checked: [],
                props: {
                    label: data => {
                        return data.name;
                    },
                    disabled: data => {
                        return data.isFixed;
                    }
                }
            },
            // 控制台权限
            dash_auth: {
                checked: 'default',
                options: [
                    {
                        value: 'default',
                        label: '系统默认'
                    },
                    {
                        value: 'stats',
                        label: '数据统计'
                    },
                    {
                        value: 'work',
                        label: '工作台'
                    }
                ]
            }
        };
    },
    mounted() {},
    methods: {
        show() {
            this.visibleDialog = true;

            return this;
        },
        /**
         * 表单注入数据
         * @param data
         */
        setData(data) {
            // 复制一个当前角色对象
            this.role_data = Object.assign({}, data);

            this.data_auth.checked = data.visible_scope.split(',');
            this.data_auth.visible_access = data.visible_access.toString();
            this.data_auth.visible_rule = data.visible_rule;

            this.dash_auth.checked = data.dashboard;

            // 获取菜单数据
            this.getMenu();

            // 获取部门数据
            this.getDept();
        },
        /**
         * 表单提交
         */
        async submit() {
            this.isSaving = true;

            // 已选择的菜单节点（选中的和半选的合并后传值接口）
            const checked_rules = this.$refs.menuRef.getCheckedKeys().concat(this.$refs.menuRef.getHalfCheckedKeys());

            // 已选择的部门节点（选中的和半选的合并后传值接口）
            const checked_depts = this.$refs.deptRef.getCheckedKeys().concat(this.$refs.deptRef.getHalfCheckedKeys());

            // 提交数据
            let _params = {
                role_id: this.role_data.id,
                permissions: checked_rules ? checked_rules.join(',') : '',
                visible_access: this.data_auth.visible_access,
                visible_scope: this.data_auth.visible_access === '4' && checked_depts ? checked_depts.join(',') : '',
                visible_rule: this.data_auth.visible_access === '5' ? this.data_auth.visible_rule : '',
                dashboard: this.dash_auth.checked
            };

            // 提交数据
            const res = await this.$API.system.role.access.post(_params);

            this.isSaving = false;

            if (res.status === 1) {
                this.$message.success(res.message);
                this.visibleDialog = false;
                this.$emit('saveSuccessEmit');
            } else {
                this.$message.warning(res.message);
            }
        },
        /**
         * 获取全部有效菜单数据
         * @returns {Promise<void>}
         */
        async getMenu() {
            const res = await this.$API.system.menu.list.get();
            this.node_auth.list = res.data;

            // 已选权限数据
            this.node_auth.checked = this.role_data.permissions ? this.role_data.permissions.split(',') : [];

            if (this.node_auth.checked.length > 0) {
                await this.$nextTick(() => {
                    let filterKeys = this.node_auth.checked.filter(key => this.$refs.menuRef.getNode(key).isLeaf);
                    this.$refs.menuRef.setCheckedKeys(filterKeys, true);
                });
            }
        },
        /**
         * 获取单位部门数据
         * @returns {Promise<void>}
         */
        async getDept() {
            const res = await this.$API.system.department.list.get();
            this.data_auth.list = res.data.list;

            // 已选部门数据
            this.data_auth.checked = this.role_data.visible_scope ? this.role_data.visible_scope.split(',') : [];

            if (this.data_auth.checked.length > 0) {
                await this.$nextTick(() => {
                    this.$refs.deptRef.setCheckedKeys(this.data_auth.checked, true);
                });
            }
        }
    }
};
</script>

<style scoped>
.el-tabs {
    &:deep(.el-tabs__item) {
        color: var(--el-text-color-secondary) !important;
    }
    &:deep(.el-tabs__item.is-active),
    &:deep(.el-tabs__item:hover) {
        color: var(--el-text-color-primary) !important;
    }
}
.treeMain {
    height: 280px;
    overflow: auto;
    border: 1px solid var(--el-border-color-dark);
    margin-bottom: 10px;
}
</style>
