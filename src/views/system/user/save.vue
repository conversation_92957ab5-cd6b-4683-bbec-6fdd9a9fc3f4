<template>
    <el-dialog
        v-model="visibleDialog"
        :title="titleMap[mode]"
        :width="640"
        :destroy-on-close="true"
        :draggable="true"
        :close-on-click-modal="false"
        @saveDialogClosedEmit="$emit('saveDialogClosedEmit')"
    >
        <el-form
            ref="userSaveForm"
            :disabled="mode === 'view'"
            :model="formData"
            :rules="rules"
            @keyup.enter="submit"
            label-position="left"
            label-width="150px"
        >
            <!--分配部门、角色、密码重置时不显示，其它都显示-->
            <template v-if="['setDepts', 'setRoles', 'resetPassword'].indexOf(mode) < 0">
                <!--<el-form-item label="头像" prop="avatar">
                    <sc-upload v-model="formData.avatar" title="上传头像"></sc-upload>
                </el-form-item>-->
                <el-form-item label="登录账号" prop="username">
                    <el-input
                        v-model="formData.username"
                        :disabled="mode !== 'add'"
                        clearable
                        placeholder="用于登录系统"
                    ></el-input>
                </el-form-item>
                <el-form-item label="用户名称" prop="realname">
                    <el-input v-model="formData.realname" clearable placeholder="请输入完整的真实姓名"></el-input>
                </el-form-item>
                <el-form-item label="手机号" prop="phone">
                    <el-input v-model="formData.phone" clearable placeholder="请输入用户手机号"></el-input>
                </el-form-item>
                <el-form-item label="邮箱账号" prop="email">
                    <el-input v-model="formData.email" clearable placeholder="请输入用户邮箱"></el-input>
                </el-form-item>
            </template>
            <!--添加、密码重置时不显示，其它都显示-->
            <template v-if="mode === 'add' || mode === 'resetPassword' || (mode === 'edit' && formData.is_super === 1)">
                <el-form-item label="登录密码" prop="password">
                    <el-input v-model="formData.password" clearable :type="passwordType">
                        <template #append>
                            <el-button type="primary" @click="generatePassword">生成随机密码</el-button>
                        </template>
                        <template #suffix>
                            <el-icon
                                :title="passwordType !== 'password' ? '隐藏密码' : '显示密码'"
                                @click="showPassword"
                                style="cursor: pointer"
                            >
                                <component :is="passwordType !== 'password' ? 'el-icon-hide' : 'el-icon-view'" />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item label="确认密码" prop="password2">
                    <el-input v-model="formData.password2" clearable :type="passwordType">
                        <template #suffix>
                            <el-icon
                                :title="passwordType !== 'password' ? '隐藏密码' : '显示密码'"
                                @click="showPassword"
                                style="cursor: pointer"
                            >
                                <component :is="passwordType !== 'password' ? 'el-icon-hide' : 'el-icon-view'" />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </template>
            <!--分配角色、密码重置时不显示，其它都显示-->
            <template v-if="['setRoles', 'resetPassword'].indexOf(mode) < 0">
                <el-form-item label="所属部门" prop="depts">
                    <el-cascader
                        v-model="formData.depts"
                        :options="depts"
                        :props="deptsProps"
                        clearable
                        style="width: 100%"
                    ></el-cascader>
                </el-form-item>
            </template>
            <!--分配部门、密码重置时不显示，其它都显示-->
            <template v-if="['setDepts', 'resetPassword'].indexOf(mode) < 0">
                <el-form-item label="角色组" prop="roles">
                    <el-select v-model="formData.roles" filterable multiple style="width: 100%">
                        <el-option v-for="item in roles" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
            </template>
            <!--分配部门、角色、密码重置时不显示，其它都显示-->
            <template v-if="['setDepts', 'setRoles', 'resetPassword'].indexOf(mode) < 0">
                <el-form-item label="用户状态" prop="status">
                    <el-switch v-model="formData.status" :active-value="1" :inactive-value="0" :inline-prompt="false" />
                </el-form-item>
            </template>
            <!--分配部门、角色时不显示，其它都显示-->
            <template v-if="['setDepts', 'setRoles'].indexOf(mode) < 0">
                <el-form-item label="登录后强制修改密码" prop="force_change_password">
                    <el-switch
                        v-model="formData.force_change_password"
                        :active-value="1"
                        :inactive-value="0"
                        :inline-prompt="false"
                    />
                </el-form-item>
            </template>
        </el-form>
        <template #footer>
            <el-button type="warning" icon="el-icon-warning-filled" @click="visibleDialog = false">取 消</el-button>
            <el-button
                v-auths="['system.user.add', 'system.user.edit']"
                v-if="mode !== 'view'"
                :loading="showLoading"
                type="primary"
                icon="el-icon-circle-check-filled"
                color="#1C409A"
                @click="submit()"
                >保 存</el-button
            >
        </template>
    </el-dialog>
</template>

<script>
export default {
    emits: ['saveSuccessEmit', 'saveDialogClosedEmit'],
    data() {
        return {
            mode: 'add',
            titleMap: {
                add: '新增用户',
                edit: '编辑用户',
                view: '查看用户',
                setDepts: '批量分配部门',
                setRoles: '批量分配角色',
                resetPassword: '批量重置密码'
            },
            visibleDialog: false, // 是否显示当前对话框
            showLoading: false, // 是否正在保存数据
            passwordType: 'password', // 密码输入框默认类型
            // 表单数据
            formData: {
                id: '',
                avatar: '',
                username: '',
                realname: '',
                gender: 2,
                phone: '',
                email: '',
                depts: '',
                roles: [],
                is_super: 0,
                force_change_password: 0
            },
            //验证规则
            rules: {
                //avatar: [{ required: true, message: '请上传头像' }],
                username: [{ required: true, message: '请输入登录账号' }],
                realname: [{ required: true, message: '请输入用户名称' }],
                phone: [{ required: true, message: '请输入用户手机号' }],
                email: [{ required: true, message: '请输入用户邮箱' }],
                password: [
                    { required: true, message: '请输入登录密码' },
                    {
                        validator: (rule, value, callback) => {
                            if (this.formData.password2 !== '') {
                                this.$refs.userSaveForm.validateField('password2');
                            }
                            callback();
                        }
                    }
                ],
                password2: [
                    { required: true, message: '请再次输入密码' },
                    {
                        validator: (rule, value, callback) => {
                            if (value !== this.formData.password) {
                                callback(new Error('两次输入密码不一致!'));
                            } else {
                                callback();
                            }
                        }
                    }
                ],
                depts: [{ required: true, message: '请选择所属部门' }],
                roles: [{ required: true, message: '请选择所属角色', trigger: 'change' }],
                status: [{ required: true, message: '用户状态' }],
                force_change_password: [{ required: true, message: '下次登录后强制修改密码' }]
            },
            // 所需数据选项
            roles: [],
            depts: [],
            deptsProps: {
                value: 'id',
                label: 'name',
                expandTrigger: 'hover', // 次级菜单的展开方式(click|hover)
                multiple: false, // 是否多选
                checkStrictly: true // 在显示复选框的情况下,是否严格遵循父子互相关联的做法
            }
        };
    },
    mounted() {
        this.getDepartment();
        this.getRoles();
    },
    methods: {
        /**
         * 显示窗口方法
         * @param mode
         * @returns {default.methods}
         */
        show(mode = 'add') {
            this.mode = mode;
            this.formData = {}; // 清空数据
            this.visibleDialog = true;
            return this;
        },
        /**
         * 表单注入数据
         * @param data
         * @param multiple 是否批量
         */
        setFormData(data, multiple) {
            const _multiple = multiple || false;

            if (_multiple === false) {
                //this.formData = Object.assign({}, data);
                Object.assign(this.formData, data);
            } else {
                // 批量操作时为ids数组
                this.formData.id = data;
                //Object.assign(this.formData.id, data);
            }
        },
        /**
         * 获取角色数据
         * @returns {Promise<void>}
         */
        async getRoles() {
            const res = await this.$API.system.role.list.get({ page_size: 100 });
            this.roles = res.data.list;
        },
        /**
         * 获取部门数据
         * @returns {Promise<void>}
         */
        async getDepartment() {
            const res = await this.$API.system.department.list.get({ page_size: 100 });
            this.depts = res.data.list;
        },
        /**
         * 生成随机密码
         */
        generatePassword() {
            const _password = this.$TOOL.generatePassword(12, null);
            this.passwordType = '';
            this.formData.password = _password;
            this.formData.password2 = _password;
        },
        /**
         * 切换密码是否显示
         */
        showPassword() {
            this.passwordType = this.passwordType === 'password' ? '' : 'password';
        },
        /**
         * 表单提交方法
         */
        submit() {
            this.$refs.userSaveForm.validate(async valid => {
                if (valid) {
                    let res;

                    // 显示对话框loading
                    this.showLoading = true;

                    switch (this.mode) {
                        case 'add':
                            // 添加接口
                            res = await this.$API.system.user.add.post(this.formData);
                            break;
                        case 'edit':
                            // 编辑接口
                            res = await this.$API.system.user.edit.post(this.formData);
                            break;
                        case 'setDepts':
                            // 分配部门接口
                            res = await this.$API.system.user.setDepts.post(this.formData);
                            break;
                        case 'setRoles':
                            // 分配角色接口
                            res = await this.$API.system.user.setRoles.post(this.formData);
                            break;
                        case 'resetPassword':
                            // 密码重置接口
                            res = await this.$API.system.user.resetPassword.post(this.formData);
                            break;
                    }

                    if (res.status === 1) {
                        // 保存成功时触发父窗口中的回调方法
                        this.$emit('saveSuccessEmit', this.formData, this.mode);

                        // 只在成功时关闭窗口
                        this.visibleDialog = false;

                        // 返回成功提示
                        this.$message.success(res.message);
                    } else {
                        this.$message.error(res.message);
                    }
                    this.showLoading = false;
                } else {
                    return false;
                }
            });
        }
    }
};
</script>

<style></style>
