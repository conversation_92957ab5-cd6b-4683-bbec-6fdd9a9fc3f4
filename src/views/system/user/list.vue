<template>
    <el-container style="overflow: hidden">
        <el-aside v-loading="treeLoading" width="260px">
            <el-container>
                <el-header>
                    <el-input v-model="deptFilterText" clearable placeholder="输入关键字进行过滤"></el-input>
                </el-header>
                <el-main class="tree-container p0">
                    <el-tree
                        ref="deptTree"
                        :data="deptData"
                        :props="deptDataProps"
                        :current-node-key="deptDefaultNodeId"
                        :expand-on-click-node="false"
                        :default-expand-all="true"
                        :filter-node-method="deptFilterNode"
                        :highlight-current="true"
                        class="menu"
                        node-key="id"
                        @node-click="deptTreeClick"
                    ></el-tree>
                </el-main>
            </el-container>
        </el-aside>
        <el-container>
            <el-header>
                <div class="left-panel">
                    <el-button
                        v-auths="['system.user.add']"
                        icon="sc-icon-user-add"
                        type="primary"
                        @click="user_add"
                        color="#1C409A"
                        >添加员工</el-button
                    >
                    <el-button
                        v-auths="['system.user.delete']"
                        :disabled="selection.length === 0"
                        icon="el-icon-delete"
                        plain
                        type="danger"
                        @click="batch_delete"
                        >删除</el-button
                    >
                    <el-button-group>
                        <el-button
                            v-auths="['system.user.setDepts']"
                            :disabled="selection.length === 0"
                            icon="el-icon-postcard"
                            @click="batch_setDepts"
                            >分配部门</el-button
                        >
                        <el-button
                            v-auths="['system.user.setRoles']"
                            :disabled="selection.length === 0"
                            icon="el-icon-connection"
                            @click="batch_setRoles"
                            >分配角色</el-button
                        >
                        <el-button
                            v-auths="['system.user.resetPassword']"
                            :disabled="selection.length === 0"
                            icon="el-icon-key"
                            @click="batch_resetPassword"
                            >密码重置</el-button
                        >
                    </el-button-group>
                </div>
                <div class="right-panel">
                    <div class="right-panel-search">
                        <el-input
                            style="min-width: 180px"
                            v-model="searchObj.keyword"
                            clearable
                            placeholder="登录账号/姓名/手机号/邮箱"
                            @keyup.enter="searchHandle"
                            @clear="searchHandle"
                        ></el-input>
                        <el-button
                            icon="el-icon-search"
                            type="primary"
                            color="#1C409A"
                            @click="searchHandle"
                        ></el-button>
                    </div>
                </div>
            </el-header>
            <el-main class="p0">
                <scTable
                    ref="userDataTable"
                    :apiObj="userDataObj"
                    remoteFilter
                    remoteSort
                    stripe
                    @selection-change="selectionChange"
                    class="custom-list-table"
                >
                    <el-table-column type="selection" width="50">
                        <template #default="scope">
                            <span v-if="scope.row.status < 0 || scope.row.is_super === 1"></span>
                        </template>
                    </el-table-column>
                    <el-table-column label="UID" prop="id" sortable="custom" width="80" fixed></el-table-column>
                    <el-table-column
                        label="登录账号"
                        prop="username"
                        sortable="custom"
                        width="150"
                        fixed
                    ></el-table-column>
                    <el-table-column
                        label="账号名称"
                        prop="realname"
                        show-overflow-tooltip
                        sortable="custom"
                        width="150"
                    ></el-table-column>
                    <el-table-column
                        label="手机号"
                        prop="phone"
                        show-overflow-tooltip
                        sortable="custom"
                        width="150"
                    ></el-table-column>
                    <el-table-column
                        label="邮箱"
                        prop="email"
                        show-overflow-tooltip
                        sortable="custom"
                        width="180"
                    ></el-table-column>
                    <el-table-column
                        label="所属部门"
                        prop="depts_name"
                        show-overflow-tooltip
                        :sortable="false"
                        width="150"
                    ></el-table-column>
                    <el-table-column
                        label="角色组"
                        prop="roles_name"
                        show-overflow-tooltip
                        :sortable="false"
                        width="200"
                    ></el-table-column>
                    <el-table-column label="性别" prop="gender" sortable="custom" align="center" width="80">
                        <template #default="scope">
                            <span v-if="scope.row.gender === 0">女</span>
                            <span v-if="scope.row.gender === 1">男</span>
                            <span v-if="scope.row.gender === 2">未知</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="出生日期"
                        prop="birthday"
                        show-overflow-tooltip
                        align="center"
                        sortable="custom"
                        width="100"
                    ></el-table-column>
                    <el-table-column
                        label="积分"
                        prop="score"
                        show-overflow-tooltip
                        sortable="custom"
                        width="100"
                    ></el-table-column>
                    <el-table-column
                        label="账户余额"
                        prop="balance"
                        show-overflow-tooltip
                        sortable="custom"
                        width="100"
                    ></el-table-column>
                    <el-table-column
                        :filters="[
                            { text: '已删除', value: '-1' },
                            { text: '已冻结', value: '0' },
                            { text: '正常', value: '1' }
                        ]"
                        column-key="filter[status]"
                        align="center"
                        label="用户状态"
                        prop="status"
                        width="100"
                    >
                        <template #default="scope">
                            <el-tooltip effect="dark" placement="right">
                                <template #content>
                                    <span v-if="scope.row.status === -1">已删除</span>
                                    <span v-if="scope.row.status === 0">已冻结</span>
                                    <span v-if="scope.row.status === 1">正常</span>
                                </template>
                                <el-switch
                                    v-auths="['system.user.status']"
                                    size="small"
                                    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #bfbfbf"
                                    v-model="scope.row.status"
                                    :loading="scope.row.loading || false"
                                    :disabled="scope.row.status < 0"
                                    :active-value="1"
                                    :inactive-value="0"
                                    @change="switchUserStatus(scope.row)"
                                />
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="加入时间"
                        prop="create_at"
                        show-overflow-tooltip
                        sortable="custom"
                        width="170"
                    >
                        <template #default="scope">
                            <span v-if="scope.row.is_super === 1"></span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="登录次数"
                        prop="login_times"
                        show-overflow-tooltip
                        sortable="custom"
                        width="100"
                    ></el-table-column>
                    <el-table-column
                        label="最后登录时间"
                        prop="last_at"
                        show-overflow-tooltip
                        sortable="custom"
                        width="170"
                    ></el-table-column>
                    <el-table-column
                        label="最后登录IP"
                        prop="last_ip"
                        show-overflow-tooltip
                        sortable="custom"
                        width="170"
                    ></el-table-column>
                    <el-table-column align="center" fixed="right" label="操作" width="180">
                        <template #default="scope">
                            <el-dropdown>
                                <el-button
                                    icon="el-icon-more"
                                    size="small"
                                    color="#28a745"
                                    style="margin: 2px 10px 0 0"
                                    type="primary"
                                    >查看</el-button
                                >
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item @click="user_view(scope.row, scope.$index)"
                                            >用户详情</el-dropdown-item
                                        >
                                        <el-dropdown-item @click="log_view(scope.row, scope.$index)"
                                            >操作日志</el-dropdown-item
                                        >
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                            <el-button
                                v-auths="['system.user.edit']"
                                size="small"
                                type="primary"
                                :disabled="scope.row.status < 0"
                                @click="user_edit(scope.row, scope.$index)"
                                color="#1C409A"
                                >编辑</el-button
                            >
                            <el-popconfirm title="确定删除该用户吗？" @confirm="user_delete(scope.row, scope.$index)">
                                <template #reference>
                                    <el-button
                                        v-auths="['system.user.delete']"
                                        size="small"
                                        type="danger"
                                        :disabled="scope.row.status < 0 || scope.row.is_super === 1"
                                        >删除</el-button
                                    >
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </scTable>
            </el-main>
        </el-container>
    </el-container>

    <!--添加编辑对话框-->
    <save-dialog
        v-if="dialog.save"
        ref="saveDialog"
        @saveDialogClosedEmit="saveDialogClosedHandle"
        @saveSuccessEmit="saveSuccessHandle"
    ></save-dialog>

    <!--操作日志对话框-->
    <el-dialog
        v-model="dialog.logList"
        title="用户操作日志"
        width="1000"
        :destroy-on-close="true"
        :draggable="true"
        :close-on-click-modal="false"
    >
        <user-log-list ref="logListRef"></user-log-list>
    </el-dialog>
</template>

<script>
import saveDialog from './save';
import userLogList from '../../profile/logs/list';
import tableConfig from '@/config/table.js';

export default {
    name: 'system.user.list',
    components: {
        saveDialog,
        userLogList
    },
    data() {
        return {
            dialog: {
                save: false,
                logList: false
            },
            deptDefaultNodeId: '', // 默认选中的部门Id
            treeLoading: false, // 显示部门数loading
            deptFilterText: '', // 部门树默认过滤词
            deptData: [], // 部门数据
            deptDataProps: {
                children: 'children',
                label: 'name'
            },
            userDataObj: this.$API.system.user.list,
            selection: [],
            searchObj: {
                keyword: null
            }
        };
    },
    watch: {
        deptFilterText(val) {
            this.$refs.deptTree.filter(val);
        }
    },
    mounted() {
        this.getDepartments();
    },
    methods: {
        /**
         * 用户添加
         */
        user_add() {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show();
            });
        },
        /**
         * 用户编辑
         * @param row
         */
        user_edit(row) {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show('edit').setFormData(row, false);
            });
        },
        /**
         * 用户查看
         * @param row
         */
        user_view(row) {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show('view').setFormData(row, false);
            });
        },
        /**
         * 用户操作日志
         * @param row
         */
        log_view(row) {
            this.dialog.logList = true;
            this.$nextTick(() => {
                this.$refs.logListRef.getLogList(row);
            });
        },
        /**
         * 单个用户删除
         * @param row
         * @param index
         * @returns {Promise<void>}
         */
        async user_delete(row, index) {
            const reqData = { id: row.id };
            const res = await this.$API.system.user.delete.post(reqData);
            if (res.code === tableConfig.successCode) {
                //这里选择刷新整个表格 OR 插入/编辑现有表格数据
                this.$refs.userDataTable.tableData.splice(index, 1);
                this.$message.success('删除成功');
            } else {
                await this.$alert(res.message, '提示', { type: 'error' });
            }
        },
        /**
         * 批量删除
         * @returns {Promise<void>}
         */
        async batch_delete() {
            this.$confirm(`确定删除选中的 ${this.selection.length} 个用户吗？`, '提示', {
                type: 'warning'
            })
                .then(async () => {
                    const loading = this.$loading();

                    let ids = this.selection.map(item => {
                        return item.id;
                    });

                    const reqData = { id: ids };

                    const res = await this.$API.system.user.delete.post(reqData);

                    if (res.code === tableConfig.successCode) {
                        this.selection.forEach(item => {
                            this.$refs.userDataTable.tableData.forEach((itemI, indexI) => {
                                if (item.id === itemI.id) {
                                    this.$refs.userDataTable.tableData.splice(indexI, 1);
                                }
                            });
                        });

                        this.$message.success(res.message);
                    } else {
                        this.$message.error(res.message);
                    }

                    loading.close();
                })
                .catch(() => {});
        },
        /**
         * 批量分配部门
         * @returns {Promise<void>}
         */
        async batch_setDepts() {
            this.dialog.save = true;

            await this.$nextTick(() => {
                let ids = this.selection.map(item => {
                    return item.id;
                });

                this.$refs.saveDialog.show('setDepts').setFormData(ids, true);
            });
        },
        /**
         * 批量分配角色
         * @returns {Promise<void>}
         */
        async batch_setRoles() {
            this.dialog.save = true;

            await this.$nextTick(() => {
                let ids = this.selection.map(item => {
                    return item.id;
                });

                this.$refs.saveDialog.show('setRoles').setFormData(ids, true);
            });
        },
        /**
         * 批量重置密码
         * @returns {Promise<void>}
         */
        async batch_resetPassword() {
            this.dialog.save = true;

            await this.$nextTick(() => {
                let ids = this.selection.map(item => {
                    return item.id;
                });

                this.$refs.saveDialog.show('resetPassword').setFormData(ids, true);
            });
        },
        /**
         * 表格选择后回调事件
         * @param selection
         */
        selectionChange(selection) {
            let _selection = [];
            selection.forEach(item => {
                if (item.status >= 0 && item.is_super === 0) {
                    _selection.push(item);
                }
            });
            this.selection = _selection;
        },
        /**
         * 加载树数据
         * @returns {Promise<void>}
         */
        async getDepartments() {
            this.treeLoading = true;
            const res = await this.$API.system.department.list.get({ page_size: 100 });
            this.treeLoading = false;
            const allNode = { id: '', name: '所有' };
            res.data.list.unshift(allNode);
            this.deptData = res.data.list;
        },
        /**
         * 树过滤
         * @param value
         * @param data
         * @returns {boolean}
         */
        deptFilterNode(value, data) {
            if (!value) return true;
            return data.name.indexOf(value) !== -1;
        },
        /**
         * 树点击事件
         * @param data
         */
        deptTreeClick(data) {
            const params = {
                dept_id: data.id
            };
            this.$refs.userDataTable.reload(data.id > 0 ? params : {});
        },
        /**
         * 搜索回调
         */
        searchHandle() {
            // 选择所有节点
            this.$refs.deptTree.setCurrentKey('');

            // 清空部门id参数
            this.searchObj.dept_id = null;

            this.$refs.userDataTable.upData(this.searchObj);
        },
        /**
         * 切换用户状态
         * @param row
         */
        switchUserStatus(row) {
            row.loading = true;

            this.$confirm(`确定要${row.status === 1 ? '解冻' : '冻结'}该用户吗？`, '提示', {
                type: 'warning'
            })
                .then(async () => {
                    const res = await this.$API.system.user.status.post({
                        id: row.id,
                        status: row.status
                    });

                    row.loading = false;

                    if (res.code === tableConfig.successCode) {
                        this.$message.success(res.message);
                    } else {
                        // 修改失败返回之前的状态
                        row.status = row.status === 1 ? 0 : 1;

                        this.$message.error(res.message);
                    }
                })
                .catch(() => {
                    row.loading = false;

                    // 修改失败返回之前的状态
                    row.status = row.status === 1 ? 0 : 1;
                });
        },
        /**
         * 保存用户对话框关闭后回调
         */
        saveDialogClosedHandle() {
            this.dialog.save = false;
        },
        /**
         * 保存用户数据成功时回调
         * @param data
         * @param mode
         */
        saveSuccessHandle(data, mode) {
            if (mode === 'add' || mode === 'setDepts' || mode === 'setRoles' || mode === 'resetPassword') {
                /*data.id = new Date().getTime();
                this.$refs.userDataTable.tableData.unshift(data);*/
                // 刷新一次列表
                this.$refs.userDataTable.refresh();
            } else if (mode === 'edit') {
                this.$refs.userDataTable.refresh();
                /*this.$refs.userDataTable.tableData.filter(item => item.id == data.id).forEach(item => {
                    Object.assign(item, data);
                });*/
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.tree-container::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}
</style>
