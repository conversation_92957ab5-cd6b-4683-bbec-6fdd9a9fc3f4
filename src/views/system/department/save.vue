<template>
    <el-dialog
        v-model="visibleDialog"
        :title="titleMap[mode]"
        :width="600"
        :destroy-on-close="true"
        :draggable="true"
        :close-on-click-modal="false"
        @saveDialogClosedEmit="$emit('saveDialogClosedEmit')"
    >
        <el-form
            ref="dialogForm"
            :disabled="mode == 'show'"
            :model="formData"
            :rules="rules"
            @keyup.enter="submit"
            label-width="100px"
        >
            <el-form-item label="上级部门" prop="dept">
                <el-cascader
                    v-model="formData.dept"
                    :options="departments"
                    :props="departmentsProps"
                    :disabled="formData.is_system == 1"
                    :placeholder="formData.is_system == 1 ? '顶级单位，无需选择' : '请选择上级单位/部门'"
                    clearable
                    style="width: 100%"
                ></el-cascader>
            </el-form-item>
            <el-form-item label="部门名称" prop="name">
                <el-input v-model="formData.name" clearable placeholder="请输入部门名称"></el-input>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
                <el-input-number
                    v-model="formData.sort"
                    :max="1000"
                    :min="-1000"
                    controls-position="right"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="是否有效" prop="status">
                <el-switch
                    v-model="formData.status"
                    :disabled="formData.is_system == 1 || formData.status < 0"
                    :active-value="1"
                    :inactive-value="0"
                ></el-switch>
            </el-form-item>
            <el-form-item label="备注" prop="description">
                <el-input v-model="formData.description" clearable type="textarea"></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="warning" icon="el-icon-warning-filled" @click="visibleDialog = false">取 消</el-button>
            <el-button
                v-auths="['system.department.add', 'system.department.edit']"
                v-if="mode !== 'show'"
                :loading="showLoading"
                type="primary"
                icon="el-icon-circle-check-filled"
                color="#1C409A"
                @click="submit()"
                >保 存</el-button
            >
        </template>
    </el-dialog>
</template>

<script>
export default {
    emits: ['saveSuccessEmit', 'saveDialogClosedEmit'],
    data() {
        return {
            mode: 'add',
            titleMap: {
                add: '新增部门',
                edit: '编辑部门',
                show: '查看部门'
            },
            visibleDialog: false,
            showLoading: false,
            //表单数据
            formData: {
                id: '',
                pid: 0,
                name: '',
                sort: 0,
                status: 1,
                description: ''
            },
            //验证规则
            rules: {
                sort: [{ required: true, message: '请输入排序', trigger: 'change' }],
                name: [{ required: true, message: '请输入部门名称' }]
            },
            //所需数据选项
            departments: [],
            departmentsProps: {
                value: 'id',
                label: 'name',
                expandTrigger: 'hover', // 次级菜单的展开方式(click|hover)
                multiple: false, // 是否多选
                checkStrictly: true // 在显示复选框的情况下,是否严格遵循父子互相关联的做法
            }
        };
    },
    mounted() {},
    methods: {
        /**
         * 显示窗口方法
         * @param mode
         * @returns {default.methods}
         */
        show(mode = 'add') {
            this.mode = mode;

            this.visibleDialog = true;

            return this;
        },
        /**
         * 表单注入数据
         * @param data
         */
        async setData(data) {
            // 加载部门树数据
            await this.getGroup();

            this.formData = Object.assign({}, data);

            // 处理部门数据状态，让其不能选择自己和子类
            this.handleDepartmentData(this.departments, data);
        },
        /**
         * 加载部门树数据
         * @returns {Promise<void>}
         */
        async getGroup() {
            const res = await this.$API.system.department.list.get({ page_size: 100, tree_mode: 1 });
            this.departments = res.data.list;
        },
        /**
         * 表单提交方法
         */
        submit() {
            this.$refs.dialogForm.validate(async valid => {
                if (valid) {
                    let res;

                    // 将选择的部门转为pid后提交
                    if (this.formData.dept) {
                        this.formData.pid = this.formData.dept.pop();
                    }

                    // 显示对话框loading
                    this.showLoading = true;

                    switch (this.mode) {
                        case 'add':
                            // 添加接口
                            res = await this.$API.system.department.add.post(this.formData);
                            break;
                        case 'edit':
                            // 编辑接口
                            res = await this.$API.system.department.edit.post(this.formData);
                            break;
                    }

                    if (res.status === 1) {
                        // 保存成功时触发父窗口中的回调方法
                        this.$emit('saveSuccessEmit', this.formData, this.mode);

                        // 只在成功时关闭窗口
                        this.visibleDialog = false;

                        // 返回成功提示
                        this.$message.success(res.message);
                    } else {
                        this.$message.error(res.message);
                    }
                    this.showLoading = false;
                }
            });
        },
        /**
         * 处理部门数据状态，让其不能选择自己和子类
         * @param depts
         * @param data
         * @param disabled
         */
        handleDepartmentData(depts, data, disabled) {
            if (depts) {
                depts.forEach(item => {
                    // 添加时默认选择的根节点数据
                    if (JSON.stringify(data) === '{}' && item.is_system == 1) {
                        this.formData.pid = item.id;
                        this.formData.dept = [item.id];
                        this.formData.sort = 0;
                        this.formData.status = 1;
                    }

                    if (data.id == item.id || disabled === true) {
                        item.disabled = true;
                    } else {
                        item.disabled = false;
                    }

                    if (item.children) {
                        // ±±±
                        this.handleDepartmentData(item.children, data, item.disabled);
                    }
                });
            }
        }
    }
};
</script>

<style></style>
