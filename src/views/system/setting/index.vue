<template>
    <el-main>
        <el-card shadow="never">
            <el-tabs
                v-model="activeTabName"
                tab-position="top"
                class="custom-tabs"
                @tab-change="tabChangeEvent"
                @tab-remove="tabRemoveEvent"
            >
                <!--循环显示选项卡-->
                <el-tab-pane
                    v-for="(group, groupKey) in groupList"
                    :key="groupKey"
                    :label888="group.label"
                    :name="group.name"
                    :lazy="true"
                >
                    <template #label>
                        <el-icon v-if="group.icon">
                            <component :is="group.icon" />
                        </el-icon>
                        <span>{{ group.label }}</span>
                    </template>
                    <el-form
                        :ref="group.ref"
                        :model="configList[groupKey]"
                        :disabled="$ACCOUNT.checkPermission('system.setting.savedata') === false"
                        label-width="120px"
                        @keyup.enter="save_group_data(groupKey)"
                        style="margin-top: 20px"
                    >
                        <el-form-item
                            v-for="(config, configKey) in configList[groupKey]"
                            :key="configKey"
                            :label="config.label"
                            :prop="config.name"
                        >
                            <!--传递给控件数据config，getWidgetData用于获取控件数据-->
                            <form-widget :data="config" @getWidgetData="getWidgetData"></form-widget>
                        </el-form-item>
                        <el-form-item>
                            <el-button
                                type="primary"
                                :disabled="
                                    !formData[groupKey] ||
                                    Object.keys(formData[groupKey]).length === 0 ||
                                    $ACCOUNT.checkPermission('system.setting.savedata') === false
                                "
                                @click="save_group_data(groupKey)"
                                >保存</el-button
                            >
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
                <!--管理分组选项卡-->
                <el-tab-pane v-if="is_manage_group" :lazy="true" :name="manage_group_tabName">
                    <template #label>
                        <el-icon><el-icon-plus /></el-icon>
                        <span>管理分组</span>
                    </template>
                    <!--引入管理分组组件-->
                    <manage-configure-group
                        @refreshSettingPageEmit="refreshSettingPage"
                        @showManageConfigItemTabEmit="showManageConfigItemTab"
                    ></manage-configure-group>
                </el-tab-pane>
                <!--某个分组的配置项管理选项卡-->
                <el-tab-pane v-if="is_manage_config" :lazy="true" :closable="true" :name="manage_config_tabName">
                    <template #label>
                        <el-icon><el-icon-list /></el-icon>
                        <span>配置项管理</span>
                    </template>
                    <!--引入管理分组配置项组件-->
                    <manage-configure-item
                        @refreshSettingPageEmit="refreshSettingPage"
                        :currentGroupObj="manage_config_groupObj"
                    ></manage-configure-item>
                </el-tab-pane>
            </el-tabs>
        </el-card>
    </el-main>
</template>

<script>
import manageConfigureGroup from '../configure_group/list';
import manageConfigureItem from '../configure/list';
import formWidget from './widget';

export default {
    name: 'system.setting.index',
    components: {
        manageConfigureGroup,
        manageConfigureItem,
        formWidget
    },
    data() {
        return {
            // 当前显示的选项卡
            activeTabName: '',
            // 分组列表数据
            groupList: {},
            // 配置项列表数据
            configList: {},
            // 是否可以管理分组
            is_manage_group: false,
            // 管理配置分组选项卡名称
            manage_group_tabName: '@manage_group',
            // 是否可以管理某个分组的配置项
            is_manage_config: false,
            // 管理配置项选项卡名称
            manage_config_tabName: '@manage_config',
            // 管理配置项时当前管理分组的数据对象
            manage_config_groupObj: {},
            // 配置分类列表数据（管理时的列表）
            configureGroupDataObj: this.$API.system.configureGroup.list,
            // 要保存的表单数据
            formData: {}
        };
    },
    created() {},
    mounted() {
        // 获取全部分组和对应数据
        this.getGroupListData();
    },
    methods: {
        /**
         * 刷新配置数据，用于子组件更新数据后刷新最新数据
         * @param _from
         */
        // eslint-disable-next-line no-unused-vars
        refreshSettingPage(_from) {
            // 获取全部分组和对应数据
            this.getGroupListData(true);
        },
        /**
         * 获取控件传递过来的数据
         * @param _data
         */
        getWidgetData(_data) {
            // 如果有修改的数据，添加到formData中
            if (_data) {
                // 判断是否有当前group对象
                if (!this.formData[_data.group_name]) {
                    this.formData[_data.group_name] = {};
                }

                // 添加一个新改变后的项
                this.formData[_data.group_name][_data.name] = _data.value;
            }
        },
        /**
         * 获取全部分组和对应数据
         * @param refresh 是否刷新数据
         * @returns {Promise<void>}
         */
        async getGroupListData(refresh) {
            const res = await this.$API.system.setting.groupListData.get();

            if (res.status === 1) {
                // 当前显示的选项卡
                this.activeTabName = refresh ? this.activeTabName : res.data.default;
                // 配置分组数据
                this.groupList = this.handleGroupData(res.data.group) || {};
                // 表单数据数据
                this.configList = res.data.config || {};

                // 这里判断是否有权限管理分组
                if (this.$ACCOUNT.checkPermission('system.configuregroup.list')) {
                    this.is_manage_group = true;
                }
            } else {
                this.$message.error(res.message);
            }
        },
        /**
         * 切换选项卡事件
         * @returns {Promise<void>}
         */
        tabChangeEvent(tabName) {
            // 获取当前tab名称
            this.activeTabName = tabName;

            // 如果切换到不是配置项管理页，且配置项管理页是打开状态（改成子组件动态监听了）
            /*if (tabName !== this.manage_config_tabName && this.is_manage_config) {
                // 关闭配置项管理页
                this.is_manage_config = false;
            }*/
        },
        /**
         * 关闭选项卡事件
         * @returns {Promise<void>}
         */
        tabRemoveEvent(tabName) {
            // 如果是配置项管理页
            if (tabName === this.manage_config_tabName) {
                // 关闭标签配置项管理选项卡
                this.showManageConfigItemTab(false, null);

                // 关闭后切换到管理分组选项卡
                this.tabChangeEvent(this.manage_group_tabName);
            }
        },
        /**
         * 分组数据处理成表单需要的格式
         * @param data
         */
        handleGroupData(data) {
            for (const item in data) {
                data[item].ref = data[item].name + 'Form';
            }
            return data;
        },
        /**
         * 保存某个分组的数据
         * @param _group
         * @returns {Promise<void>}
         */
        async save_group_data(_group) {
            if (!this.formData[_group] || Object.keys(this.formData[_group]).length === 0) {
                return;
            }

            // 保存数据
            const res = await this.$API.system.setting.saveData.post({ group: _group, data: this.formData[_group] });

            if (res.status === 1) {
                // 保存完清空当前选项卡未保存的数据
                this.formData[_group] = {};

                this.$message.success('更新成功');
            } else {
                this.$message.error(res.message);
            }
        },
        /**
         * 用于控制管理分组配置项选项卡列表是否显示
         */
        showManageConfigItemTab(_isShow, _group) {
            // 打开新标签
            this.is_manage_config = _isShow;

            // 将当前分组的数据信息传递到新标签
            this.manage_config_groupObj = _group;

            // 切换到管理配置项标签页
            this.tabChangeEvent(this.manage_config_tabName);
        }
    }
};
</script>

<style lang="scss" scoped>
.custom-tabs {
    &:deep(.el-tabs__item) {
        color: var(--el-text-color-secondary) !important;
        .el-icon {
            position: relative;
            top: 2px;
            margin-right: 2px;
        }
    }
    &:deep(.el-tabs__item.is-active),
    &:deep(.el-tabs__item:hover) {
        color: var(--el-text-color-primary) !important;
    }
}

.tab-add-button {
    position: relative;
    float: right;
    margin-right: -5px;
    margin-top: 4px;
    z-index: 1;
}
</style>
