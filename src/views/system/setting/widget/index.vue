<template>
    <!--input控件-->
    <input-widget v-if="data.type === 'input'" :data="data" @getChildWidgetData="getChildWidgetData"></input-widget>
    <!--textarea控件-->
    <textarea-widget
        v-if="data.type === 'textarea'"
        :data="data"
        @getChildWidgetData="getChildWidgetData"
    ></textarea-widget>
    <!--select控件-->
    <select-widget v-if="data.type === 'select'" :data="data" @getChildWidgetData="getChildWidgetData"></select-widget>
    <!--checkbox控件-->
    <checkbox-widget
        v-if="data.type === 'checkbox'"
        :data="data"
        @getChildWidgetData="getChildWidgetData"
    ></checkbox-widget>
    <!--radio控件-->
    <radio-widget v-if="data.type === 'radio'" :data="data" @getChildWidgetData="getChildWidgetData"></radio-widget>
    <!--password控件-->
    <password-widget
        v-if="data.type === 'password'"
        :data="data"
        @getChildWidgetData="getChildWidgetData"
    ></password-widget>
    <!--switch控件-->
    <switch-widget v-if="data.type === 'switch'" :data="data" @getChildWidgetData="getChildWidgetData"></switch-widget>
    <!--number控件-->
    <number-widget v-if="data.type === 'number'" :data="data" @getChildWidgetData="getChildWidgetData"></number-widget>
    <!--date控件-->
    <date-widget v-if="data.type === 'date'" :data="data" @getChildWidgetData="getChildWidgetData"></date-widget>
</template>

<script>
import inputWidget from './components/input';
import textareaWidget from './components/textarea';
import selectWidget from './components/select';
import checkboxWidget from './components/checkbox';
import radioWidget from './components/radio';
import passwordWidget from './components/password';
import switchWidget from './components/switch';
import numberWidget from './components/number';
import dateWidget from './components/date';

export default {
    name: 'widget',
    components: {
        inputWidget,
        textareaWidget,
        selectWidget,
        checkboxWidget,
        radioWidget,
        passwordWidget,
        switchWidget,
        numberWidget,
        dateWidget
    },
    props: {
        data: { type: Object, default: () => {} }
    },
    data() {
        return {};
    },
    methods: {
        /**
         * 将接收到的子组件数据传输给父组件
         * @param _data
         */
        getChildWidgetData(_data) {
            this.$emit('getWidgetData', _data);
        }
    }
};
</script>

<style lang="scss" scoped></style>
