<template>
    <el-switch
        v-model="item.value"
        :size="item.size"
        :active-value="true"
        :inactive-value="false"
        @input="transferWidgetData(item)"
    ></el-switch>
    <description-widget :data="data"></description-widget>
</template>

<script>
import descriptionWidget from './description';

export default {
    name: 'switchWidget',
    components: {
        descriptionWidget
    },
    props: {
        data: { type: Object, default: () => {} }
    },
    data() {
        return {
            item: {}
        };
    },
    created() {
        this.item.name = this.data.name || '';

        // 开关转为bool类型
        this.item.value = !!parseInt(this.data.value);
        this.item.size = this.data.size || 'default';
        this.item.description = this.data.description || '';

        // 合并对象，传递的时候完整给父组件
        this.item = { ...this.data, ...this.item };
    },
    methods: {
        /**
         * 传输组件数据给父组件
         * @param _data
         */
        transferWidgetData(_data) {
            let _itemData = Object.assign({}, _data);

            // 转换为数值型
            _itemData.value = Number(_itemData.value);

            this.$emit('getChildWidgetData', _itemData);
        }
    }
};
</script>

<style scoped></style>
