<template>
    <el-checkbox
        v-model="item.name"
        :label="_item.label"
        v-for="(_item, _index) in item.options"
        :key="_index"
        @input="transferWidgetData(item)"
    ></el-checkbox>
    <description-widget :data="data"></description-widget>
</template>

<script>
import descriptionWidget from './description';

export default {
    name: 'checkboxWidget',
    components: {
        descriptionWidget
    },
    props: {
        data: { type: Object, default: () => {} }
    },
    data() {
        return {
            item: {}
        };
    },
    created() {
        this.item.name = this.data.name || '';
        this.item.value = this.data.value || '';
        this.item.options = this.data.options || {};

        // 合并对象，传递的时候完整给父组件
        this.item = { ...this.data, ...this.item };
    },
    methods: {
        /**
         * 传输组件数据给父组件
         * @param _data
         */
        transferWidgetData(_data) {
            let _itemData = Object.assign({}, _data);

            this.$emit('getChildWidgetData', _itemData);
        }
    }
};
</script>

<style scoped></style>
