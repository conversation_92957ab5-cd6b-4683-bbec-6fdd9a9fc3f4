<template>
    <el-select
        v-model="item.value"
        :size="item.size"
        :multiple="item.config.multiple"
        :placeholder="item.placeholder"
        @change="transferWidgetData(item)"
        style="width: 100%"
    >
        <el-option v-for="(value, key) in item.options" :key="key" :label="value" :value="key"></el-option>
    </el-select>
</template>

<script>
import descriptionWidget from './description';

export default {
    name: 'selectWidget',
    components: {
        descriptionWidget
    },
    props: {
        data: { type: Object, default: () => {} }
    },
    data() {
        return {
            item: {}
        };
    },
    created() {
        this.item.name = this.data.name || '';

        this.item.size = this.data.size || 'default';
        this.item.description = this.data.description || '';

        // 合并对象，传递的时候完整给父组件
        this.item = { ...this.data, ...this.item };

        // 是否多选
        this.item.config.multiple = this.item.config.multiple || false;

        let _default_value = this.data.value;

        if (!_default_value) {
            _default_value = this.item.config.default;
        }

        // 已选值（数组）
        this.item.value = _default_value ? _default_value.split(',') : [];

        this.item.options = this.item.options || {};
    },
    methods: {
        /**
         * 传输组件数据给父组件
         * @param _data
         */
        transferWidgetData(_data) {
            let _itemData = Object.assign({}, _data);

            // 转换为字符串
            _itemData.value = _itemData.value ? _itemData.value.join(',') : '';

            this.$emit('getChildWidgetData', _itemData);
        }
    }
};
</script>

<style scoped></style>
