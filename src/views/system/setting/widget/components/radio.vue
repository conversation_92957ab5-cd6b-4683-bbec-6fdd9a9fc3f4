<template>
    <el-radio-group v-model="item.value">
        <el-radio
            size="large"
            v-model="item.name"
            :value="_item.label"
            v-for="(_item, _index) in item.options"
            :key="_index"
            >{{ item.title }}</el-radio
        >
    </el-radio-group>
</template>

<script>
export default {
    name: 'radioWidget',
    props: {
        data: { type: Object, default: () => {} }
    },
    data() {
        return {
            item: {}
        };
    },
    created() {
        this.item.name = this.data.name || '';
        this.item.value = this.data.value || '';
        this.item.options = this.data.options || {};

        // 合并对象，传递的时候完整给父组件
        this.item = { ...this.data, ...this.item };
    },
    methods: {
        /**
         * 传输组件数据给父组件
         * @param _data
         */
        transferWidgetData(_data) {
            let _itemData = Object.assign({}, _data);

            this.$emit('getChildWidgetData', _itemData);
        }
    }
};
</script>

<style scoped></style>
