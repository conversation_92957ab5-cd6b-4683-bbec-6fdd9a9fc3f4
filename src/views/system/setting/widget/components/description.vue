<template>
    <div class="el-form-item-msg" v-if="msg">
        <el-icon><el-icon-question-filled /></el-icon>
        <span v-html="msg"></span>
    </div>
</template>

<script>
export default {
    name: 'descriptionWidget',
    props: {
        data: { type: Object, default: () => {} }
    },
    data() {
        return {
            msg: ''
        };
    },
    created() {
        this.msg = this.data.description || '';
    }
};
</script>

<style lang="scss" scoped></style>
