<template>
    <el-input v-model="item.value" :placeholder="item.placeholder" :size="item.size"></el-input>
    <description-widget :data="data"></description-widget>
</template>

<script>
import descriptionWidget from './description';

export default {
    name: 'dateWidget',
    components: {
        descriptionWidget
    },
    props: {
        data: { type: Object, default: () => {} }
    },
    data() {
        return {
            item: {}
        };
    },
    created() {
        this.item.name = this.data.name || '';
        this.item.value = this.data.value || '';
        this.item.placeholder = this.data.placeholder || '';
        this.item.description = this.data.description || '';
        this.item.size = this.data.size || 'default';

        // 合并对象，传递的时候完整给父组件
        this.item = { ...this.data, ...this.item };
    },
    methods: {
        /**
         * 传输组件数据给父组件
         * @param _data
         */
        transferWidgetData(_data) {
            let _itemData = Object.assign({}, _data);

            this.$emit('getChildWidgetData', _itemData);
        }
    }
};
</script>

<style scoped></style>
