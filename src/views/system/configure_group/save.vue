<template>
    <el-dialog
        v-model="visibleDialog"
        :title="titleMap[mode]"
        :width="580"
        :destroy-on-close="true"
        :draggable="true"
        :close-on-click-modal="false"
        @saveDialogClosedEmit="$emit('saveDialogClosedEmit')"
    >
        <el-form
            ref="dialogForm"
            :disabled="mode == 'show'"
            :model="formData"
            :rules="rules"
            @keyup.enter="submit"
            label-position="left"
            label-width="100px"
        >
            <el-form-item label="分组名称" prop="name">
                <el-input v-model="formData.name" clearable></el-input>
            </el-form-item>
            <el-form-item label="分组别名" prop="label">
                <el-input v-model="formData.label" clearable></el-input>
            </el-form-item>
            <el-form-item label="分组图标" prop="icon">
                <sc-icon-select v-model="formData.icon" clearable></sc-icon-select>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
                <el-input-number
                    v-model="formData.sort"
                    :max="1000"
                    :min="-1000"
                    controls-position="right"
                    style="width: 100%"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="是否有效" prop="status">
                <el-switch
                    v-model="formData.status"
                    :disabled="formData.is_system == 1 || formData.status < 0"
                    :active-value="1"
                    :inactive-value="0"
                    :inline-prompt="false"
                />
            </el-form-item>
            <el-form-item label="是否隐藏" prop="hidden">
                <el-switch
                    v-model="formData.hidden"
                    :disabled="formData.is_system == 1 || formData.status < 0"
                    :active-value="1"
                    :inactive-value="0"
                    :inline-prompt="false"
                />
            </el-form-item>
            <el-form-item label="分组描述" prop="description">
                <el-input v-model="formData.description" clearable type="textarea"></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="warning" icon="el-icon-warning-filled" @click="visibleDialog = false">取 消</el-button>
            <el-button
                v-auths="['system.configuregroup.add', 'system.configuregroup.edit']"
                v-if="mode !== 'show'"
                :loading="showLoading"
                type="primary"
                icon="el-icon-circle-check-filled"
                color="#1C409A"
                @click="submit()"
                >保 存</el-button
            >
        </template>
    </el-dialog>
</template>

<script>
import scIconSelect from '@/components/scIconSelect';

export default {
    components: {
        scIconSelect
    },
    emits: ['saveSuccessEmit', 'saveDialogClosedEmit'],
    data() {
        return {
            mode: 'add',
            titleMap: {
                add: '新增分组',
                edit: '编辑分组',
                show: '查看分组'
            },
            visibleDialog: false,
            showLoading: false, // 是否正在保存数据
            //表单数据
            formData: {
                id: '',
                name: '',
                label: '',
                description: '',
                sort: 0,
                hidden: 0,
                status: 1
            },
            //验证规则
            rules: {
                name: [{ required: true, message: '请输入分组名称' }],
                label: [{ required: true, message: '请输入分组别名' }],
                sort: [{ required: true, message: '请输入排序', trigger: 'change' }]
            }
        };
    },
    mounted() {},
    methods: {
        /**
         * 显示窗口方法
         * @param mode
         * @returns {default.methods}
         */
        show(mode = 'add') {
            this.mode = mode;
            this.formData = {}; // 清空数据
            this.formData.sort = 0; // 排序号默认值
            this.formData.hidden = 0; // 是否隐藏默认值
            this.formData.status = 1; // 状态默认值
            this.visibleDialog = true;
            return this;
        },
        /**
         * 表单注入数据
         * @param data
         */
        setData(data) {
            //this.formData = Object.assign({}, data);
            Object.assign(this.formData, data);
        },
        /**
         * 表单提交方法
         */
        submit() {
            this.$refs.dialogForm.validate(async valid => {
                if (valid) {
                    let res;

                    // 显示对话框loading
                    this.showLoading = true;

                    switch (this.mode) {
                        case 'add':
                            // 添加接口
                            res = await this.$API.system.configureGroup.add.post(this.formData);
                            break;
                        case 'edit':
                            // 编辑接口
                            res = await this.$API.system.configureGroup.edit.post(this.formData);
                            break;
                    }

                    if (res.status === 1) {
                        // 保存成功时触发父窗口中的回调方法
                        this.$emit('saveSuccessEmit', this.formData, this.mode);

                        // 只在成功时关闭窗口
                        this.visibleDialog = false;

                        // 返回成功提示
                        this.$message.success(res.message);
                    } else {
                        this.$message.error(res.message);
                    }
                    this.showLoading = false;
                }
            });
        }
    }
};
</script>

<style></style>
