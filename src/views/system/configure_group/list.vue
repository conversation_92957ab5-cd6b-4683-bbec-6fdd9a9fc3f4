<template>
    <el-alert
        title="操作提示："
        description="系统默认自带了配置分组，且不可删除，配置相关操作应该由系统管理员操作，防止相关逻辑出现错误！"
        type="warning"
        effect="dark"
        :show-icon="true"
        :closable="false"
        class="custom-alert"
    />
    <el-header class="custom-header">
        <div class="left-panel">
            <el-button
                v-auths="['system.configuregroup.add']"
                icon="el-icon-plus"
                type="primary"
                @click="group_add"
                color="#1C409A"
                >添加分组</el-button
            >
        </div>

        <div class="right-panel">
            <div class="right-panel-search">
                <el-input
                    style="min-width: 225px"
                    clearable
                    v-model="searchObj.keyword"
                    placeholder="分组名称 / 分组别名 / 分组描述"
                    @keyup.enter="searchHandle"
                    @clear="searchHandle"
                ></el-input>
                <el-button icon="el-icon-search" type="primary" color="#1C409A" @click="searchHandle"></el-button>
            </div>
        </div>
    </el-header>

    <scTable
        ref="configureGroupTable"
        :apiObj="configureGroupDataObj"
        :params="configureGroupParams"
        :hideDo="true"
        :hidePagination="true"
        remoteFilter
        remoteSort
        row-key="id"
        stripe
        class="custom-table"
    >
        <el-table-column label="ID" prop="id" sortable="custom" width="80"></el-table-column>
        <el-table-column label="分组名称" prop="label" width="100"></el-table-column>
        <el-table-column label="分组别名" prop="name" width="100"></el-table-column>
        <el-table-column label="分组图标" prop="icon" width="80" align="center">
            <template #default="scope">
                <el-icon v-if="scope.row.icon">
                    <component :is="scope.row.icon" />
                </el-icon>
            </template>
        </el-table-column>
        <el-table-column label="分组描述" show-overflow-tooltip min-width="150" prop="description"></el-table-column>
        <el-table-column label="是否系统" prop="is_system" width="100">
            <template #default="scope">
                <el-tag v-if="scope.row.is_system === 1" type="success">是</el-tag>
                <el-tag v-if="scope.row.is_system === 0" type="danger">否</el-tag>
            </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" width="80"></el-table-column>
        <el-table-column
            :filters="[
                { text: '已删除', value: '-1' },
                { text: '已冻结', value: '0' },
                { text: '正常', value: '1' }
            ]"
            column-key="filter[status]"
            v-auths="['system.configuregroup.sort']"
            align="center"
            label="状态"
            prop="status"
            width="80"
        >
            <template #default="scope">
                <el-tooltip effect="dark" placement="right">
                    <template #content>
                        <span v-if="scope.row.status === -1">已删除</span>
                        <span v-if="scope.row.status === 0">已冻结</span>
                        <span v-if="scope.row.status === 1">正常</span>
                    </template>
                    <el-switch
                        size="small"
                        style="--el-switch-on-color: #13ce66; --el-switch-off-color: #bfbfbf"
                        v-model="scope.row.status"
                        :loading="scope.row.loading || false"
                        :disabled="scope.row.is_system === 1 || scope.row.status < 0"
                        :active-value="1"
                        :inactive-value="0"
                        @change="switchStatus($event, scope.row)"
                    />
                </el-tooltip>
            </template>
        </el-table-column>
        <!--<el-table-column v-auths="['system.configuregroup.hidden']" align="center" label="是否隐藏" prop="hidden" width="90">
            <template #default="scope">
                <el-tooltip effect="dark" placement="right">
                    <template #content>
                        <span v-if="scope.row.hidden == 0">正常</span>
                        <span v-if="scope.row.hidden == 1">隐藏</span>
                    </template>
                    <el-switch
                        size="small"
                        style="&#45;&#45;el-switch-on-color: #13ce66; &#45;&#45;el-switch-off-color: #bfbfbf"
                        v-model="scope.row.hidden"
                        :loading="scope.row.loading || false"
                        :disabled="scope.row.is_system == 1 || scope.row.status < 0"
                        :active-value="0"
                        :inactive-value="1"
                        @change="switchHidden($event, scope.row)"
                    />
                </el-tooltip>
            </template>
        </el-table-column>-->
        <el-table-column label="创建时间" prop="create_at" sortable="custom" width="180"></el-table-column>
        <el-table-column align="center" fixed="right" label="操作" width="290">
            <template #default="scope">
                <el-button
                    size="small"
                    type="success"
                    :disabled="scope.row.status < 0"
                    @click="open_group_config_list(scope.row, scope.$index)"
                    v-auths="['system.configure.list']"
                    color="#6a2ea1"
                    icon="el-icon-list"
                    >配置项管理</el-button
                >
                <el-button
                    size="small"
                    type="primary"
                    :disabled="scope.row.status < 0"
                    @click="group_edit(scope.row, scope.$index)"
                    v-auths="['system.configuregroup.edit']"
                    icon="el-icon-edit"
                    color="#1C409A"
                    >编辑</el-button
                >
                <el-popconfirm title="确定删除该分组吗？" @confirm="group_delete(scope.row, scope.$index)">
                    <template #reference>
                        <el-button
                            size="small"
                            type="danger"
                            :disabled="scope.row.status < 0 || scope.row.is_system === 1"
                            v-auths="['system.configuregroup.delete']"
                            icon="el-icon-delete"
                            >删除</el-button
                        >
                    </template>
                </el-popconfirm>
            </template>
        </el-table-column>
    </scTable>

    <!--添加编辑分组对话框-->
    <save-dialog
        v-if="dialog.save"
        ref="saveDialog"
        @saveDialogClosedEmit="saveDialogClosedHandle"
        @saveSuccessEmit="handleSaveSuccess"
    ></save-dialog>
</template>

<script>
import saveDialog from './save';

export default {
    name: 'manageConfigureGroup',
    components: {
        saveDialog
    },
    emits: ['refreshSettingPageEmit', 'showManageConfigItemTabEmit'],
    data() {
        return {
            dialog: {
                save: false
            },
            // 配置分类列表数据（管理时的列表）
            configureGroupDataObj: this.$API.system.configureGroup.list,
            // 配置分类列表数据接口请求参数
            configureGroupParams: {
                page_size: 100
            },
            searchObj: {
                keyword: null
            }
        };
    },
    mounted() {},
    methods: {
        /**
         * 添加分组
         */
        group_add() {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show();
            });
        },
        /**
         * 编辑分组
         * @param row
         */
        group_edit(row) {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show('edit').setData(row);
            });
        },
        /**
         * 删除分组
         * @param row
         * @returns {Promise<void>}
         */
        async group_delete(row, index) {
            const reqData = { id: row.id };
            const res = await this.$API.system.configureGroup.delete.post(reqData);
            if (res.status === 1) {
                this.$refs.configureGroupTable.refresh();
                this.$message.success('删除成功');
            } else {
                await this.$alert(res.message, '提示', { type: 'error' });
            }
        },
        /**
         * 打开分组配置项管理
         * @param row
         * @param index
         */
        open_group_config_list(row) {
            // 触发父窗口显示
            this.$emit('showManageConfigItemTabEmit', true, row);
        },
        /**
         * 表格内开关
         * @param val
         * @param row
         */
        switchStatus(val, row) {
            row.loading = true;

            this.$confirm(`确定要${row.status === 1 ? '启用' : '禁用'}该分组【${row.name}】吗？`, '提示', {
                type: 'warning'
            })
                .then(async () => {
                    const res = await this.$API.system.configureGroup.status.post({
                        id: row.id,
                        status: row.status
                    });

                    row.loading = false;

                    if (res.status === 1) {
                        this.$message.success(res.message);
                    } else {
                        // 修改失败返回之前的状态
                        row.status = row.status === 1 ? 0 : 1;

                        this.$message.error(res.message);
                    }
                })
                .catch(() => {
                    row.loading = false;

                    // 修改失败返回之前的状态
                    row.status = row.status === 1 ? 0 : 1;
                });
        },
        /**
         * 表格内隐藏开关
         * @param val
         * @param row
         */
        switchHidden(val, row) {
            row.loading = true;

            this.$confirm(`确定要${row.hidden === 1 ? '隐藏' : '显示'}该分组【${row.name}】吗？`, '提示', {
                type: 'warning'
            })
                .then(async () => {
                    const res = await this.$API.system.configureGroup.hidden.post({
                        id: row.id,
                        hidden: row.hidden
                    });

                    row.loading = false;

                    if (res.status === 1) {
                        this.$message.success(res.message);
                    } else {
                        // 修改失败返回之前的状态
                        row.hidden = row.hidden === 1 ? 0 : 1;

                        this.$message.error(res.message);
                    }
                })
                .catch(() => {
                    row.loading = false;

                    // 修改失败返回之前的状态
                    row.hidden = row.hidden === 1 ? 0 : 1;
                });
        },
        /**
         * 搜索回调
         */
        searchHandle() {
            this.$refs.configureGroupTable.upData(this.searchObj);
        },
        /**
         * 保存分组对话框关闭后回调
         */
        saveDialogClosedHandle() {
            this.dialog.save = false;
        },
        /**
         * 本地更新数据
         * @param data
         * @param mode
         */
        // eslint-disable-next-line no-unused-vars
        handleSaveSuccess(data, mode) {
            this.$refs.configureGroupTable.refresh();

            // 触发父级窗口事件回调
            this.$emit('refreshSettingPageEmit', 'configure_group_list');
        }
    }
};
</script>

<style lang="scss" scoped>
.custom-alert {
    background-color: rgba(36, 75, 115, 0.5) !important;
}
.el-header {
    border-bottom: none;
    padding: 0;
    margin-bottom: 15px;
}
.custom-table {
    margin-top: 20px;
}
</style>
