<template>
    <el-drawer
        custom-class="drawerBG"
        direction="rtl"
        v-model="visible"
        :destroy-on-close="true"
        :title="titleMap[mode]"
        :size="800"
        :before-close="beforeCloseEvent"
        @saveDrawerClosedEmit="$emit('saveDrawerClosedEmit')"
    >
        <el-main style="padding: 0 20px">
            <el-card shadow="never">
                <el-form
                    ref="editForm"
                    :disabled="mode == 'show' || formData.system == 1"
                    :model="formData"
                    :rules="rules"
                    label-position="left"
                    label-width="100px"
                >
                    <el-form-item label="所属分组" prop="group">
                        <el-select v-model="formData.group" disabled style="width: 100%">
                            <el-option :label="groupInfo.label" :value="groupInfo.name"></el-option>
                        </el-select>
                        <div class="el-form-item-msg">
                            <el-icon><el-icon-question-filled /></el-icon>
                            <span>用于显示到对应的配置分组中，一般无需选择</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="配置标识" prop="name">
                        <el-input
                            v-model="formData.name"
                            :disabled="formData.system == 1 || formData.id > 0"
                            clearable
                        ></el-input>
                        <div class="el-form-item-msg">
                            <el-icon><el-icon-question-filled /></el-icon>
                            <span>只能使用英文且不能重复</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="配置名称" prop="label">
                        <el-input v-model="formData.label" clearable></el-input>
                        <div class="el-form-item-msg">
                            <el-icon><el-icon-question-filled /></el-icon>
                            <span>用于后台显示的配置名称</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="控件类型" prop="type">
                        <el-select v-model="formData.type" style="width: 100%">
                            <el-option
                                v-for="(value, key) in widgetTypes"
                                :key="key"
                                :label="value"
                                :value="key"
                            ></el-option>
                        </el-select>
                        <div class="el-form-item-msg">
                            <el-icon><el-icon-question-filled /></el-icon>
                            <span>系统会根据不同类型解析配置值</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="控件配置" prop="options" v-if="formData.type in enumTypes">
                        <el-input v-model="formData.config" type="textarea"></el-input>
                        <div class="el-form-item-msg">
                            <el-icon><el-icon-question-filled /></el-icon>
                            <span>控件配置项，如select控件多选时：`{"multiple":true}`。必须为json数据格式</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="配&ensp;置&ensp;项" prop="options" v-if="formData.type in enumTypes">
                        <el-input v-model="formData.options" type="textarea"></el-input>
                        <div class="el-form-item-msg">
                            <el-icon><el-icon-question-filled /></el-icon>
                            <span>如果是枚举型，需要配置该项。必须为json数据格式</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="默&ensp;认&ensp;值" prop="value">
                        <el-input v-model="formData.value" clearable type="textarea"></el-input>
                        <div class="el-form-item-msg">
                            <el-icon><el-icon-question-filled /></el-icon>
                            <span>控件的默认值。可以为空</span>
                        </div>
                    </el-form-item>
                    <el-form-item
                        label="占&ensp;位&ensp;符"
                        prop="placeholder"
                        v-if="formData.type in placeholderWidgets"
                    >
                        <el-input v-model="formData.placeholder" clearable></el-input>
                        <div class="el-form-item-msg">
                            <el-icon><el-icon-question-filled /></el-icon>
                            <span>控件的placeholder提示占位符</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="配置描述" prop="description">
                        <el-input v-model="formData.description" clearable type="textarea"></el-input>
                        <div class="el-form-item-msg">
                            <el-icon><el-icon-question-filled /></el-icon>
                            <span>填写后会作为当前控件下面的描述（支持html）。如这里！👈🏻</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="排&ensp;序&ensp;号" prop="sort">
                        <el-input-number
                            v-model="formData.sort"
                            :max="1000"
                            :min="-1000"
                            controls-position="right"
                            style="width: 100%"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item label="是否有效" prop="status">
                        <el-switch
                            v-model="formData.status"
                            :disabled="formData.system == 1 || formData.status < 0"
                            :active-value="1"
                            :inactive-value="0"
                            :inline-prompt="false"
                        />
                    </el-form-item>
                </el-form>
            </el-card>
        </el-main>
        <template #footer>
            <div style="flex: auto">
                <el-button type="warning" icon="el-icon-warning-filled" @click="cancelConfirm">取 消</el-button>
                <el-button
                    v-if="mode != 'show'"
                    :loading="showLoading"
                    type="primary"
                    icon="el-icon-circle-check-filled"
                    color="#1C409A"
                    @click="submitConfirm"
                    v-auths="['system.configure.add', 'system.configure.edit']"
                    >保 存</el-button
                >
            </div>
        </template>
    </el-drawer>
</template>

<script>
export default {
    emits: ['saveSuccessEmit', 'saveDrawerClosedEmit'],
    props: {
        // 接收父组件传来的分组信息
        groupInfo: {
            type: Object,
            default: () => {}
        },
        // 接收父组件传来的控件列表数据
        widgetTypes: {
            type: Object,
            default: () => {}
        },
        // 接收父组件传来的枚举类型控件数据
        enumTypes: {
            type: Object,
            default: () => {}
        },
        // 接收父组件传来的占位符类型控件数据
        placeholderWidgets: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            mode: 'add',
            titleMap: {
                add: '新增配置项',
                edit: '编辑配置项',
                show: '查看配置项'
            },
            visible: false,
            showLoading: false, // 是否正在保存数据
            //表单数据
            formData: {
                id: '',
                group: '',
                name: '',
                label: '',
                value: '',
                type: '',
                config: '',
                options: '',
                placeholder: '',
                description: '',
                sort: 0,
                system: 0,
                hidden: 0,
                status: 1
            },
            //验证规则
            rules: {
                /*name: [{ required: true, message: '请输入配置项名称' }],
                label: [{ required: true, message: '请输入配置项别名' }],
                sort: [{ required: true, message: '请输入排序', trigger: 'change' }]*/
            }
        };
    },
    mounted() {
        if (Object.keys(this.widgetTypes).length === 0) {
            this.$message.error('未获取到控件类型列表');
        }
        if (Object.keys(this.enumTypes).length === 0) {
            this.$message.error('未获取到枚举类型控件列表');
        }
        if (Object.keys(this.placeholderWidgets).length === 0) {
            this.$message.error('未获取到带占位符的控件列表');
        }
    },
    methods: {
        /**
         * 显示窗口方法
         * @param mode
         * @returns {default.methods}
         */
        show(mode = 'add') {
            this.mode = mode;
            this.formData = {}; // 清空数据
            this.formData.group = this.groupInfo.name; // 所属分组
            this.formData.type = 'input'; // 默认选中的控件类型
            this.formData.sort = 0; // 排序号默认值
            this.formData.system = 0; // 是否系统默认值
            this.formData.hidden = 0; // 是否隐藏默认值
            this.formData.status = 1; // 状态默认值
            this.visible = true;
            return this;
        },
        /**
         * 表单注入数据
         * @param data
         */
        setData(data) {
            //this.formData = Object.assign({}, data);
            Object.assign(this.formData, data);
        },
        /**
         * 表单取消提交方法
         * @returns {boolean}
         */
        cancelConfirm() {
            this.visible = false;
        },
        /**
         * 表单提交方法
         */
        submitConfirm() {
            this.$refs.editForm.validate(async valid => {
                if (valid) {
                    let res;

                    // 显示对话框loading
                    this.showLoading = true;

                    switch (this.mode) {
                        case 'add':
                            // 添加接口
                            res = await this.$API.system.configure.add.post(this.formData);
                            break;
                        case 'edit':
                            // 编辑接口
                            res = await this.$API.system.configure.edit.post(this.formData);
                            break;
                    }

                    if (res.status === 1) {
                        // 保存成功时触发父窗口中的回调方法
                        this.$emit('saveSuccessEmit', this.formData, this.mode);

                        // 只在成功时关闭窗口
                        this.visible = false;

                        // 返回成功提示
                        this.$message.success(res.message);
                    } else {
                        this.$message.error(res.message);
                    }
                    this.showLoading = false;
                }
            });
        },
        /**
         * 弹窗关闭前回调事件
         * @returns {boolean}
         */
        beforeCloseEvent() {
            //this.$message.warning('取消编辑请点击取消');
            return false;
        }
    }
};
</script>

<style lang="scss" scoped></style>
