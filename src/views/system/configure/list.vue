<template>
    <el-alert
        title="操作提示："
        description="配置项使用后尽量不要随意变动，否则可能导致意外问题的发生。该操作建议由系统管理员操作。"
        type="success"
        effect="dark"
        :show-icon="true"
        :closable="false"
        class="custom-alert"
    />
    <el-header class="custom-header">
        <div class="left-panel">
            <el-button
                v-auths="['system.configure.add']"
                icon="el-icon-plus"
                type="primary"
                @click="config_add"
                color="#1C409A"
                >添加配置项
            </el-button>
        </div>

        <div class="right-panel">
            <div class="right-panel-search">
                <el-input
                    style="min-width: 280px"
                    clearable
                    v-model="searchObj.keyword"
                    placeholder="配置项名称 / 别名 / 描述 / 配置值 / 提示"
                    @keyup.enter="searchHandle"
                    @clear="searchHandle"
                ></el-input>
                <el-button icon="el-icon-search" type="primary" color="#1C409A" @click="searchHandle"></el-button>
            </div>
        </div>
    </el-header>

    <scTable
        ref="configureItemTable"
        :apiObj="configureItemDataObj"
        :params="configureItemParams"
        :hideDo="false"
        :hidePagination="false"
        remoteFilter
        remoteSort
        row-key="id"
        stripe
        class="custom-table"
    >
        <el-table-column label="ID" prop="id" sortable="custom" width="80"></el-table-column>
        <el-table-column label="所属分组" show-overflow-tooltip prop="group_label" width="120">
            <template #default="scope"> 【{{ scope.row.group_label }}】</template>
        </el-table-column>
        <el-table-column label="配置项名称" show-overflow-tooltip prop="label" width="120"></el-table-column>
        <el-table-column label="配置项别名" show-overflow-tooltip prop="name" width="150"></el-table-column>
        <el-table-column label="配置项类型" prop="type" width="120">
            <template #default="scope"> 【{{ configureWidgetTypes[scope.row.type] }}】</template>
        </el-table-column>
        <el-table-column label="配置项描述" show-overflow-tooltip min-width="150" prop="description"></el-table-column>
        <el-table-column label="控件占位符" show-overflow-tooltip min-width="150" prop="placeholder"></el-table-column>
        <el-table-column label="是否系统" prop="system" width="100">
            <template #default="scope">
                <el-tag v-if="scope.row.system === 1" type="success">是</el-tag>
                <el-tag v-if="scope.row.system === 0" type="danger">否</el-tag>
            </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" width="80"></el-table-column>
        <el-table-column
            :filters="[
                { text: '已删除', value: '-1' },
                { text: '已冻结', value: '0' },
                { text: '正常', value: '1' }
            ]"
            column-key="filter[status]"
            align="center"
            label="状态"
            prop="status"
            width="80"
        >
            <template #default="scope">
                <el-tooltip effect="dark" placement="right">
                    <template #content>
                        <span v-if="scope.row.status === -1">已删除</span>
                        <span v-if="scope.row.status === 0">已冻结</span>
                        <span v-if="scope.row.status === 1">正常</span>
                    </template>
                    <el-switch
                        size="small"
                        style="--el-switch-on-color: #13ce66; --el-switch-off-color: #bfbfbf"
                        v-model="scope.row.status"
                        :loading="scope.row.loading || false"
                        :disabled="scope.row.system === 1 || scope.row.status < 0"
                        :active-value="1"
                        :inactive-value="0"
                        @change="switchStatus($event, scope.row)"
                    />
                </el-tooltip>
            </template>
        </el-table-column>
        <!--<el-table-column align="center" label="是否隐藏" prop="hidden" width="90">
            <template #default="scope">
                <el-tooltip effect="dark" placement="right">
                    <template #content>
                        <span v-if="scope.row.hidden == 0">正常</span>
                        <span v-if="scope.row.hidden == 1">隐藏</span>
                    </template>
                    <el-switch
                        size="small"
                        style="&#45;&#45;el-switch-on-color: #13ce66; &#45;&#45;el-switch-off-color: #bfbfbf"
                        v-model="scope.row.hidden"
                        :loading="scope.row.loading || false"
                        :disabled="scope.row.system == 1 || scope.row.status < 0"
                        :active-value="0"
                        :inactive-value="1"
                        @change="switchHidden($event, scope.row)"
                    />
                </el-tooltip>
            </template>
        </el-table-column>-->
        <el-table-column label="创建时间" prop="create_at" sortable="custom" width="180"></el-table-column>
        <el-table-column label="更新时间" prop="update_at" sortable="custom" width="180"></el-table-column>
        <el-table-column align="center" fixed="right" label="操作" width="170">
            <template #default="scope">
                <el-button
                    size="small"
                    type="primary"
                    :disabled="scope.row.status < 0"
                    @click="config_edit(scope.row, scope.$index)"
                    v-auths="['system.configure.edit']"
                    color="#1C409A"
                    icon="el-icon-edit"
                    style="color: #ffffff"
                    >编辑
                </el-button>
                <el-popconfirm title="确定删除该配置项吗？" @confirm="config_delete(scope.row, scope.$index)">
                    <template #reference>
                        <el-button
                            size="small"
                            type="danger"
                            :disabled="scope.row.status < 0 || scope.row.system === 1"
                            v-auths="['system.configure.delete']"
                            icon="el-icon-delete"
                            >删除
                        </el-button>
                    </template>
                </el-popconfirm>
            </template>
        </el-table-column>
    </scTable>

    <!--添加编辑对话框-->
    <save-drawer
        v-if="drawer.save"
        ref="saveDrawer"
        :groupInfo="currentGroupObj"
        :widgetTypes="configureWidgetTypes"
        :enumTypes="configureEnumWidgets"
        :placeholderWidgets="configurePlaceholderWidgets"
        @saveDrawerClosedEmit="saveDrawerClosedHandle"
        @saveSuccessEmit="handleSaveSuccess"
    ></save-drawer>
</template>

<script>
import saveDrawer from './save';

export default {
    name: 'manageConfigureItem',
    components: {
        saveDrawer
    },
    emits: ['refreshSettingPageEmit'],
    props: {
        // 接收父组件传来的数据
        currentGroupObj: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            // 抽屉对象
            drawer: {
                save: false
            },
            // 控件类型列表
            configureWidgetTypes: {},
            // 枚举控件类型列表
            configureEnumWidgets: {},
            // 有占位符控件类型列表
            configurePlaceholderWidgets: {},
            // 配置项列表数据接口（管理时的列表）
            configureItemDataObj: this.$API.system.configure.list,
            // 配置项列表数据接口请求参数
            configureItemParams: {},
            // 搜索对象
            searchObj: {
                keyword: null
            }
        };
    },
    created() {
        // 将父组件传过来的分组名称，传递到请求接口中
        this.configureItemParams.group = this.currentGroupObj.name;

        // 获取所有控件类型
        this.get_widget_types();
    },
    computed: {
        /**
         * 动态获取当前分组名称
         * @returns {*}
         */
        getCurrentGroupName() {
            return this.currentGroupObj;
        }
    },
    watch: {
        /**
         * 监听当前分组是否改变
         * @param newVal
         * @param oldVal
         */
        getCurrentGroupName(newVal, oldVal) {
            if (newVal !== oldVal) {
                // 更新为最新的分组名称
                this.configureItemParams.group = this.currentGroupObj.name;

                // 触发重载表格数据
                this.handleSaveSuccess('reload', this.configureItemParams);
            }
        }
    },
    methods: {
        /**
         * 获取所有控件类型
         * @returns {Promise<void>}
         */
        async get_widget_types() {
            const res = await this.$API.system.configure.widgets.get();

            if (res.status === 1) {
                for (const key in res.data.list) {
                    // 简化控件类型数据
                    this.configureWidgetTypes[key] = res.data.list[key].label;

                    // 枚举控件类型数据
                    if (res.data.list[key].enum) {
                        this.configureEnumWidgets[key] = res.data.list[key].label;
                    }

                    // 有占位符控件类型数据
                    if (res.data.list[key].placeholder) {
                        this.configurePlaceholderWidgets[key] = res.data.list[key].label;
                    }
                }
            } else {
                this.$message.error('控件类型获取失败');
            }
        },
        /**
         * 添加配置项
         */
        config_add() {
            this.drawer.save = true;
            this.$nextTick(() => {
                this.$refs.saveDrawer.show();
            });
        },
        /**
         * 编辑配置项
         * @param row
         */
        config_edit(row) {
            this.drawer.save = true;
            this.$nextTick(() => {
                this.$refs.saveDrawer.show('edit').setData(row);
            });
        },
        /**
         * 删除配置项
         * @param row
         * @returns {Promise<void>}
         */
        async config_delete(row) {
            const reqData = { id: row.id };
            const res = await this.$API.system.configure.delete.post(reqData);
            if (res.status === 1) {
                // 刷新表格
                this.handleSaveSuccess('refresh');

                this.$message.success('删除成功');
            } else {
                await this.$alert(res.message, '提示', { type: 'error' });
            }
        },
        /**
         * 表格内开关
         * @param val
         * @param row
         */
        switchStatus(val, row) {
            row.loading = true;

            this.$confirm(`确定要${row.status === 1 ? '启用' : '禁用'}该配置项【${row.name}】吗？`, '提示', {
                type: 'warning'
            })
                .then(async () => {
                    const res = await this.$API.system.configure.status.post({
                        id: row.id,
                        status: row.status
                    });

                    row.loading = false;

                    if (res.status === 1) {
                        this.$message.success(res.message);
                    } else {
                        // 修改失败返回之前的状态
                        row.status = row.status === 1 ? 0 : 1;

                        this.$message.error(res.message);
                    }
                })
                .catch(() => {
                    row.loading = false;

                    // 修改失败返回之前的状态
                    row.status = row.status === 1 ? 0 : 1;
                });
        },
        /**
         * 表格内隐藏开关
         * @param val
         * @param row
         */
        switchHidden(val, row) {
            row.loading = true;

            this.$confirm(`确定要${row.hidden === 1 ? '隐藏' : '显示'}该配置项【${row.name}】吗？`, '提示', {
                type: 'warning'
            })
                .then(async () => {
                    const res = await this.$API.system.configure.hidden.post({
                        id: row.id,
                        hidden: row.hidden
                    });

                    row.loading = false;

                    if (res.status === 1) {
                        this.$message.success(res.message);
                    } else {
                        // 修改失败返回之前的状态
                        row.hidden = row.hidden === 1 ? 0 : 1;

                        this.$message.error(res.message);
                    }
                })
                .catch(() => {
                    row.loading = false;

                    // 修改失败返回之前的状态
                    row.hidden = row.hidden === 1 ? 0 : 1;
                });
        },
        /**
         * 搜索回调
         */
        searchHandle() {
            this.$refs.configureItemTable.upData(this.searchObj);
        },
        /**
         * 保存配置项对话框关闭后回调
         */
        saveDrawerClosedHandle() {
            this.drawer.save = false;
        },
        /**
         * 本地更新数据
         * @param mode
         */
        handleSaveSuccess(mode, params) {
            if (mode === 'reload') {
                this.$refs.configureItemTable.reload(params);
            } else {
                // 触发父级窗口事件回调
                this.$emit('refreshSettingPageEmit', 'configure_list');

                this.$refs.configureItemTable.refresh();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.custom-alert {
    background-color: rgba(91, 179, 107, 0.6) !important;
}
.el-header {
    border-bottom: none;
    padding: 0;
    margin-bottom: 15px;
}
.custom-table {
    margin-top: 20px;
}
</style>
