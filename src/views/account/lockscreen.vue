<template>
    <el-form
        ref="lockscreenForm"
        :model="formData"
        :rules="rules"
        label-width="0"
        size="large"
        @keyup.enter="unLockscreen"
        class="lockscreen_form"
    >
        <el-form-item prop="password">
            <el-input v-model="formData.password" placeholder="请输入解锁密码" show-password></el-input>
            <div class="el-form-item-msg">
                <el-icon class="el-icon--left">
                    <el-icon-question-filled />
                </el-icon>
                <span>解锁密码默认为您的账户密码</span>
            </div>
        </el-form-item>
        <el-row :gutter="10" class="footer">
            <el-col :span="12">
                <el-button type="info" icon="el-icon-user" style="width: 100%" @click="logoutSystem">
                    退出登录
                </el-button>
            </el-col>
            <el-col :span="12">
                <el-button
                    :loading="loading"
                    icon="el-icon-unlock"
                    color="#339257"
                    type="primary"
                    @click="unLockscreen"
                    style="width: 100%"
                    >解锁屏幕</el-button
                >
            </el-col>
        </el-row>
    </el-form>
</template>

<script>
import account from '@/utils/account';
import { lockScreenManager } from '@/utils/lockscreen';

export default {
    data() {
        return {
            formData: {
                password: ''
            },
            rules: {
                password: [{ required: true, message: '' }]
            },
            loading: false,
            disabled: false, // 是否禁用
            retryCount: 0 // 重试次数
        };
    },
    methods: {
        /**
         * 解锁屏幕方式
         */
        async unLockscreen() {
            console.log('开始解锁流程'); // 调试日志
            if (this.loading) {
                console.log('已在加载中，退出'); // 调试日志
                return;
            }

            this.loading = true;
            console.log('准备验证表单'); // 调试日志
            const validate = await this.$refs.lockscreenForm.validate().catch(err => {
                console.error('表单验证失败:', err); // 调试日志
            });

            if (!validate) {
                console.log('表单验证未通过'); // 调试日志
                this.loading = false;
                return false;
            }

            try {
                console.log('发起解锁请求，密码:', this.formData.password); // 调试日志
                const success = await lockScreenManager.unlock(this.formData.password);
                console.log('解锁结果:', success); // 调试日志
                if (success) {
                    this.formData.password = ''; // 清空密码
                }
            } catch (error) {
                console.error('解锁过程出错:', error);
            } finally {
                this.loading = false;
            }
        },

        /**
         * 退出登录系统方式
         */
        logoutSystem() {
            return account.logoutSystem();
        }
    }
};
</script>

<style lang="scss" scoped></style>
