<template>
    <el-form label-width="0" size="large">
        <el-alert :show-icon="true" :closable="false" effect="light" class="custom-alert" type="success">
            <template #title>
                <!--<el-icon class="smile-icon"><sc-icon-smile /></el-icon>-->
                <div class="wrapper">
                    <span class="title">
                        <b>{{ userInfo.realname }}</b
                        >，欢迎您回来！
                    </span>
                    <hr />
                    <ul class="content">
                        <li><b>本次登录时间：</b>{{ $TOOL.dateFormat(userInfo.login_at) }}</li>
                        <li><b>上次登录时间：</b>{{ $TOOL.dateFormat(userInfo.last_login_at) }}</li>
                        <li><b>本次登录IP：</b>{{ $TOOL.long2ip(userInfo.login_ip) }}</li>
                        <li><b>上次登录IP：</b>{{ $TOOL.long2ip(userInfo.last_login_ip) }}</li>
                        <!--<li><b>登录次数：</b>{{ userInfo.login_times }}</li>-->
                    </ul>
                </div>
            </template>
        </el-alert>
        <el-form-item>
            <el-button :round="false" class="btn btn-enter" type="primary" @click="loginSystem">进入系统</el-button>
        </el-form-item>
        <el-form-item>
            <el-button :round="false" class="btn btn-exit" type="danger" @click="logoutSystem">退出登录</el-button>
        </el-form-item>
    </el-form>
</template>

<script>
import account from '@/utils/account';
import storageConfig from '@/config/storage';
import tool from '@/utils/tool';

/**
 * 已登录状态时用户信息展示
 */
export default {
    props: {
        // 父组件传过来的用户信息
        userInfo: {
            type: Object,
            default: () => {
                return {};
            }
        }
    },
    data() {
        return {
            form: {
                password: ''
            }
        };
    },
    mounted() {},
    methods: {
        /**
         * 登入系统
         */
        async loginSystem() {
            const _access_token = tool.cookie.get(storageConfig.vars.accessToken);
            const _refresh_token = tool.data.get(storageConfig.vars.refreshToken);
            const _user_info = tool.data.get(storageConfig.vars.userInfo);
            const _user_menu = tool.data.get(storageConfig.vars.userMenu);
            const _user_permission = tool.data.get(storageConfig.vars.userPermission);

            if (_access_token && _refresh_token && _user_info && _user_menu && _user_permission) {
                this.enterSystem();
            } else {
                this.$TOOL.data.clear();
                this.$TOOL.cookie.remove(storageConfig.vars.accessToken);
                return window.location.reload();
            }
        },
        /**
         * 进入系统dashboard
         */
        enterSystem() {
            return this.$router.replace({ path: '/dashboard' });
        },
        /**
         * 退出登录系统方式
         */
        logoutSystem() {
            return account.logoutSystem(true);
        }
    }
};
</script>

<style lang="scss" scoped>
.custom-alert {
    color: #816937;
    background-color: rgba(219, 207, 183, 63%) !important;
    margin-bottom: 20px;

    :deep(.el-alert__icon) {
        font-size: 42px;
        width: 48px;
        opacity: 0.45;
    }

    :deep(.el-alert__content) {
        width: 100%;

        .content {
            padding: 0;
        }
    }

    .wrapper {
        line-height: 1;
        padding: 10px 0;
        font-size: 12px;

        .title {
            font-size: 14px;
        }

        ul,
        li {
            list-style: none;
        }
        li {
            line-height: 2;
        }
    }

    hr {
        border-top-color: #bae8b6;
        margin: 10px 0;
        border: 0;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        box-sizing: content-box;
        height: 0;
        overflow: visible;
    }
}

.btn {
    &.btn-enter {
        background-color: #3eaa82;
        border-color: #329671;
        width: 100%;
        height: 42px;
        opacity: 0.65;
        color: #fff;

        &:focus,
        &:hover {
            background-color: #009a61;
            border-color: #008151;
            box-shadow: 0 0 0 0.325rem rgb(38 169 121 / 35%);
        }
    }
    &.btn-exit {
        background-color: #f86464;
        border-color: #f74c4c;
        width: 100%;
        height: 42px;
        opacity: 0.65;
        color: #fff;

        &:focus,
        &:hover {
            background-color: #f74747;
            border-color: #f52727;
            box-shadow: 0 0 0 0.325rem rgb(249 123 123 / 35%);
        }
    }
}
</style>
