<template>
    <!--用户锁屏对话框-->
    <el-dialog
        v-model="isLocked"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :draggable="true"
        :show-close="false"
        :show-fullscreen="false"
        title="屏幕解锁"
        custom-class="lockscreen_dialog"
        modal-class="lockscreen_modal"
        width="480px"
    >
        <template #header="{ titleId, titleClass }">
            <div class="dialog-header">
                <el-icon class="el-icon--left">
                    <el-icon-unlock />
                </el-icon>
                <span :id="titleId" :class="titleClass">屏幕解锁</span>
            </div>
        </template>
        <lockscreen ref="lockscreenForm"></lockscreen>
    </el-dialog>
</template>

<script>
import { useGlobalStore } from '@/stores/global';
import { useLockscreenStore } from '@/stores/lockscreen';
import lockscreen from './lockscreen';

export default {
    name: 'lockScreenDialog',
    components: {
        lockscreen
    },
    computed: {
        isLocked: {
            get() {
                return useLockscreenStore().isLocked;
            },
            set(value) {
                useLockscreenStore().setLockStatus(value);
            }
        }
    },
    methods: {
        /**
         * 引用全局状态管理
         */
        useGlobalStore
    }
};
</script>

<style lang="scss" scoped>
.lockscreen_dialog {
    .el-dialog__body {
        padding: 0 0 10px 0;
    }
}

:global(.lockscreen_modal) {
    backdrop-filter: blur(10px);
    background-color: rgba(0, 0, 0, 0.6) !important;
}
</style>
