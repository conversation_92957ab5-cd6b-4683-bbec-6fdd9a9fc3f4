<template>
    <el-container>
        <el-header>
            <div class="left-panel">
                <el-button
                    v-auths="['tenant.manage.add']"
                    icon="el-icon-plus"
                    type="primary"
                    @click="tenant_add"
                    color="#1C409A"
                    >添加租户</el-button
                >
                <el-button
                    v-auths="['tenant.manage.delete']"
                    :disabled="selection.length === 0"
                    icon="el-icon-delete"
                    plain
                    type="danger"
                    @click="batch_delete"
                    >删除</el-button
                >
            </div>
            <div class="right-panel">
                <div class="right-panel-search">
                    <el-input
                        style="min-width: 225px"
                        v-model="searchObj.keyword"
                        clearable
                        placeholder="租户名称 / 租户编码 / 租户描述"
                        @keyup.enter="searchHandle"
                        @clear="searchHandle"
                    ></el-input>
                    <el-button icon="el-icon-search" type="primary" color="#1C409A" @click="searchHandle"></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="p0">
            <scTable
                ref="tenantDataTable"
                :apiObj="tenantDataObj"
                :cell-class-name="tableCellClassName"
                remoteFilter
                remoteSort
                row-key="id"
                stripe
                @selection-change="selectionChange"
                @row-dblclick="rowDblclickHandle"
                class="custom-list-table"
            >
                <el-table-column type="selection" width="50"></el-table-column>
                <el-table-column label="ID" prop="id" sortable="custom" width="80" fixed></el-table-column>
                <el-table-column label="租户名称" prop="name" width="150" fixed show-overflow-tooltip></el-table-column>
                <el-table-column label="租户编码" prop="code" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column label="联系人" prop="contact_person" width="100" show-overflow-tooltip></el-table-column>
                <el-table-column
                    label="联系电话"
                    prop="contact_phone"
                    width="150"
                    show-overflow-tooltip
                ></el-table-column>
                <el-table-column label="最大用户数" prop="max_users" width="100">
                    <template #default="scope">
                        <span>{{ scope.row.max_users === 0 ? '不限制' : scope.row.max_users }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    :filters="[
                        { text: '已删除', value: '-1' },
                        { text: '已冻结', value: '0' },
                        { text: '正常', value: '1' }
                    ]"
                    column-key="filter[status]"
                    align="center"
                    label="状态"
                    prop="status"
                >
                    <template #default="scope">
                        <el-tooltip effect="dark" placement="right">
                            <template #content>
                                <span v-if="scope.row.status === -1">已删除</span>
                                <span v-if="scope.row.status === 0">已冻结</span>
                                <span v-if="scope.row.status === 1">正常</span>
                            </template>
                            <div style="display: flex; align-items: center;">
                                <el-tag
                                    :type="scope.row.status === 1 ? 'success' : scope.row.status === 0 ? 'warning' : 'danger'"
                                    size="small"
                                >
                                    {{ scope.row.status === 1 ? '正常' : scope.row.status === 0 ? '已冻结' : '已删除' }}
                                </el-tag>
                                <el-switch
                                    v-auths="['tenant.manage.status']"
                                    size="small"
                                    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #bfbfbf"
                                    v-model="scope.row.status"
                                    :loading="scope.row.loading || false"
                                    :active-value="1"
                                    :inactive-value="0"
                                    @change="switchTenantStatus($event, scope.row)"
                                />
                            </div>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column label="过期时间" prop="expire_time" align="center" width="180">
                    <template #default="scope">
                        <span>{{ scope.row.expire_time === 0 ? '永不过期' : new Date(scope.row.expire_time * 1000).toLocaleString('zh-CN', {year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit'}).replace(/\//g, '-') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="create_at" sortable="custom" width="180"></el-table-column>
                <el-table-column
                    label="租户描述"
                    show-overflow-tooltip
                    min-width="150"
                    prop="description"
                ></el-table-column>
                <el-table-column label="排序" prop="sort" sortable="custom" width="80">
                    <template #default="scope">
                        <!--v-if去判断双击的是不是当前单元格-->
                        <!--回车才能提交，blur还原原来值-->
                        <el-input
                            v-if="scope.row.index + ',' + scope.column.index === columnEditObj.currentCellPos"
                            v-model="scope.row.sort"
                            :ref="scope.row.index + ',' + scope.column.index"
                            placeholder="请输入排序号"
                            size="small"
                            style="width: 60px"
                            @keyup.enter="hideCellEditInput(true)"
                            @blur="hideCellEditInput(false)"
                        >
                        </el-input>
                        <span v-else>
                            <el-tooltip effect="dark" placement="right">
                                <template #content>
                                    <label>双击编辑</label>
                                </template>
                                <label>{{ scope.row.sort }}</label>
                            </el-tooltip>
                        </span>
                    </template>
                </el-table-column>
                <el-table-column align="center" fixed="right" label="操作" width="220">
                    <template #default="scope">
                        <el-button
                            size="small"
                            type="success"
                            @click="tenant_view(scope.row, scope.$index)"
                            color="#28a745"
                            >查看</el-button
                        >
                        <el-button
                            v-auths="['tenant.manage.edit']"
                            size="small"
                            type="primary"
                            :disabled="scope.row.status < 0"
                            @click="tenant_edit(scope.row, scope.$index)"
                            color="#1C409A"
                            >编辑</el-button
                        >
                        <el-popconfirm title="确定删除该租户吗？" @confirm="tenant_delete(scope.row, scope.$index)">
                            <template #reference>
                                <el-button
                                    v-auths="['tenant.manage.delete']"
                                    size="small"
                                    type="danger"
                                    :disabled="scope.row.status < 0"
                                    >删除</el-button
                                >
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </scTable>
        </el-main>
    </el-container>

    <!--添加编辑对话框-->
    <save-dialog
        v-if="dialog.save"
        ref="saveDialog"
        @saveDialogClosedEmit="saveDialogClosedHandle"
        @saveSuccessEmit="handleSaveSuccess"
    ></save-dialog>
</template>

<script>
import saveDialog from './save';
import ScTable from '@/components/scTable/index.vue';
import tableConfig from '@/config/table.js';

export default {
    name: 'tenant.manage.list',
    components: {
        ScTable,
        saveDialog
    },
    data() {
        return {
            // 行编辑对象
            columnEditObj: {
                // 要编辑的当前行原始数据
                currentColumnOld: {},
                // 要编辑的当前行原始数据
                currentColumnNew: {},
                // 要编辑的当前单元格坐标
                currentCellPos: null
            },
            dialog: {
                save: false
            },
            tenantDataObj: this.$API.tenant.manage.list,
            selection: [],
            searchObj: {
                keyword: null
            }
        };
    },
    methods: {
        /**
         * 添加
         */
        tenant_add() {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show();
            });
        },
        /**
         * 租户编辑
         * @param row
         */
        tenant_edit(row) {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show('edit').setData(row);
            });
        },
        /**
         * 租户查看
         * @param row
         */
        tenant_view(row) {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show('show').setData(row);
            });
        },
        /**
         * 租户删除
         * @param row
         * @returns {Promise<void>}
         */
        async tenant_delete(row) {
            const reqData = { id: row.id };
            const res = await this.$API.tenant.manage.delete.post(reqData);
            if (res.status === 1) {
                this.$refs.tenantDataTable.refresh();
                this.$message.success('删除成功');
            } else {
                await this.$alert(`操作失败：${res.message} (CODE:${res.code})`, '提示', { type: 'error' });
            }
        },
        /**
         * 批量删除
         * @returns {Promise<void>}
         */
        async batch_delete() {
            try {
                await this.$confirm(`确定删除选中的 ${this.selection.length} 项租户组吗？`, '提示', {
                    type: 'warning'
                });

                const loading = this.$loading();
                const ids = this.selection.map(item => item.id);
                const reqData = { ids: ids };
                const res = await this.$API.tenant.manage.batchDelete.post(reqData);

                loading.close();

                if (res.status === 1) {
                    this.$refs.tenantDataTable.refresh();
                    this.$message.success(res.message || '批量删除成功');
                    this.selection = [];
                } else {
                    this.$message.error(`操作失败：${res.message} (CODE:${res.code})`);
                }
            } catch (e) {
                if (e !== 'cancel') {
                    this.$message.error('操作异常，请稍后重试');
                }
            }
        },
        /**
         * 表格选择后回调事件
         * @param selection
         */
        selectionChange(selection) {
            let _selection = [];
            selection.forEach(item => {
                if (item.status >= 0) {
                    _selection.push(item);
                }
            });
            this.selection = _selection;
        },
        /**
         * 表格内开关
         * @param val
         * @param row
         */
        switchTenantStatus(val, row) {
            row.loading = true;

            this.$confirm(`确定要${row.status === 1 ? '启用' : '禁用'}该租户组【${row.name}】吗？`, '提示', {
                type: 'warning'
            })
                .then(async () => {
                    const res = await this.$API.tenant.manage.status.put({
                        id: row.id,
                        status: row.status
                    }).catch(e => ({
                        code: e.response?.status || 500,
                        message: e.message || '网络请求异常'
                    }));

                    row.loading = false;

                    if (res.status === 1) {
                        this.$message.success(res.message);
                    } else {
                        // 修改失败返回之前的状态
                        row.status = row.status === 1 ? 0 : 1;

                        this.$message.error(`操作失败：${res.message} (CODE:${res.code})`);
                    }
                })
                .catch(() => {
                    row.loading = false;

                    // 修改失败返回之前的状态
                    row.status = row.status === 1 ? 0 : 1;
                });
        },
        /**
         * 搜索回调
         */
        searchHandle() {
            this.$refs.tenantDataTable.upData(this.searchObj);
        },
        /**
         * 根据ID获取树结构
         * @param id
         * @returns {null}
         */
        filterTree(id) {
            let target = null;

            function filter(tree) {
                tree.forEach(item => {
                    if (item.id === id) {
                        target = item;
                    }
                    if (item.children) {
                        filter(item.children);
                    }
                });
            }

            filter(this.$refs.tenantDataTable.tableData);
            return target;
        },
        /**
         * 保存租户对话框关闭后回调
         */
        saveDialogClosedHandle() {
            this.dialog.save = false;
        },
        /**
         * 本地更新数据
         * @param data
         * @param mode
         */
        handleSaveSuccess(data, mode) {
            if (mode === 'add') {
                this.$refs.tenantDataTable.refresh();
            } else if (mode === 'edit') {
                this.$refs.tenantDataTable.refresh();
            } else {
                this.$refs.tenantDataTable.refresh();
            }
        },
        /**
         * 给单元格绑定横向和竖向的index，这样就能确定是哪一个单元格
         * @param row
         * @param column
         * @param rowIndex
         * @param columnIndex
         */
        tableCellClassName({ row, column, rowIndex, columnIndex }) {
            row.index = rowIndex;
            column.index = columnIndex;
        },
        /**
         * 表格行编辑回调事件
         * @param row
         * @param column
         */
        rowDblclickHandle(row, column) {
            // 单元格是sort，且未编辑时可编辑
            if (column.property === 'sort' && JSON.stringify(this.columnEditObj.currentColumnOld) === '{}') {
                this.columnEditObj.currentColumnNew = row;
                // 拷贝一个当前行的原始数据对象
                this.columnEditObj.currentColumnOld = Object.assign({}, row);
                this.columnEditObj.currentCellPos = row.index + ',' + column.index;
            } else {
                return false;
            }
        },
        /**
         * 当input失去焦点的时候，隐藏input
         */
        async hideCellEditInput(submit) {
            // 记录原来的值
            const _old_sort = this.columnEditObj.currentColumnOld.sort;

            if (submit === true) {
                const _sort = parseInt(this.columnEditObj.currentColumnNew.sort);

                const res = await this.$API.tenant.manage.sort.post({
                    id: this.columnEditObj.currentColumnOld.id,
                    sort: _sort
                });

                if (res.status === 1) {
                    this.$message.success(res.message);

                    // 设置成新值
                    this.columnEditObj.currentColumnNew.sort = _sort;

                    // 切换后刷新表格
                    this.$refs.tenantDataTable.refresh();
                } else {
                    // 修改失败返回之前的值
                    this.columnEditObj.currentColumnNew.sort = _old_sort;

                    this.$message.error(`操作失败：${res.message} (CODE:${res.code})`);
                }
            } else {
                // 还原成原来的值
                this.columnEditObj.currentColumnNew.sort = _old_sort;
            }

            // 清空编辑的数据对象
            this.columnEditObj.currentCellPos = null;
            this.columnEditObj.currentColumnNew = {};
            this.columnEditObj.currentColumnOld = {};
        }
    }
};
</script>

<style lang="scss" scoped></style>
