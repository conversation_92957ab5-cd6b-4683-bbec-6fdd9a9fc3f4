<template>
    <el-dialog
        v-model="visibleDialog"
        :title="titleMap[mode]"
        :width="580"
        :destroy-on-close="true"
        :draggable="true"
        :close-on-click-modal="false"
        @saveDialogClosedEmit="$emit('saveDialogClosedEmit')"
    >
        <el-form
            ref="dialogForm"
            :disabled="mode === 'show'"
            :model="formData"
            :rules="rules"
            @keyup.enter="submit"
            label-position="left"
            label-width="100px"
        >
            <el-form-item label="租户名称" prop="name">
                <el-input v-model="formData.name" clearable></el-input>
            </el-form-item>
            <el-form-item label="租户编码" prop="code">
                <el-input v-model="formData.code" :disabled="mode !== 'add'" clearable></el-input>
            </el-form-item>
            <el-form-item label="联系人" prop="contact_person">
                <el-input v-model="formData.contact_person" clearable></el-input>
            </el-form-item>
            <el-form-item label="联系电话" prop="contact_phone">
                <el-input v-model="formData.contact_phone" clearable></el-input>
            </el-form-item>
            <el-form-item label="最大用户数" prop="max_users">
                <el-input-number
                    v-model="formData.max_users"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="过期时间" prop="expire_time">
                <el-date-picker
                    v-model="formData.expire_time"
                    type="datetime"
                    placeholder="选择过期时间，不选择则永不过期"
                    style="width: 100%"
                    clearable
                    @clear="formData.expire_time = 0"
                ></el-date-picker>
            </el-form-item>
            <el-form-item label="租户描述" prop="description">
                <el-input v-model="formData.description" clearable type="textarea"></el-input>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
                <el-input-number
                    v-model="formData.sort"
                    :max="1000"
                    :min="-1000"
                    controls-position="right"
                    style="width: 100%"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="是否有效" prop="status">
                <el-switch
                    v-model="formData.status"
                    :disabled="formData.is_system === 1 || formData.status < 0"
                    :active-value="1"
                    :inactive-value="0"
                    :inline-prompt="false"
                />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="warning" icon="el-icon-warning-filled" @click="visibleDialog = false">取 消</el-button>
            <el-button
                v-auths="['system.role.add', 'system.role.edit']"
                v-if="mode !== 'show'"
                :loading="showLoading"
                type="primary"
                icon="el-icon-circle-check-filled"
                color="#1C409A"
                @click="submit()"
                >保 存</el-button
            >
        </template>
    </el-dialog>
</template>

<script>
export default {
    emits: ['saveSuccessEmit', 'saveDialogClosedEmit'],
    data() {
        return {
            mode: 'add',
            titleMap: {
                add: '新增租户',
                edit: '编辑租户',
                show: '查看租户'
            },
            visibleDialog: false,
            showLoading: false, // 是否正在保存数据
            //表单数据
            formData: {
                id: '',
                name: '',
                code: '',
                contact_person: '',
                contact_phone: '',
                max_users: 0,
                expire_time: 0,
                description: '',
                sort: 0,
                status: 1
            },
            //验证规则
            rules: {
                name: [
                    { required: true, message: '请输入租户名称' },
                    { min: 2, max: 50, message: '租户名称长度在2-50个字符之间' }
                ],
                code: [
                    { required: true, message: '请输入租户编码' },
                    { pattern: /^[a-zA-Z0-9_-]+$/, message: '租户编码只能包含字母、数字、下划线和横线' },
                    { min: 2, max: 30, message: '租户编码长度在2-30个字符之间' }
                ],
                contact_person: [
                    { max: 50, message: '联系人长度不能超过50个字符' }
                ],
                contact_phone: [
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式' }
                ],
                max_users: [
                    { type: 'number', message: '请输入正确的用户数量' }
                ],
                sort: [{ required: true, message: '请输入排序', trigger: 'change' }],
                description: [{ max: 200, message: '租户描述最多200个字符' }]
            }
        };
    },
    mounted() {},
    methods: {
        /**
         * 显示窗口方法
         * @param mode
         * @returns {default.methods}
         */
        show(mode = 'add') {
            this.mode = mode;
            this.formData = {}; // 清空数据
            this.formData.sort = 0; // 排序号默认值
            this.formData.status = 1; // 状态默认值
            this.formData.max_users = 0; // 最大用户数默认值
            this.formData.expire_time = 0; // 过期时间默认值为永不过期
            this.visibleDialog = true;
            return this;
        },
        /**
         * 表单注入数据
         * @param data
         */
        setData(data) {
            //this.formData = Object.assign({}, data);
            Object.assign(this.formData, data);
        },
        /**
         * 表单提交方法
         */
        submit() {
            this.$refs.dialogForm.validate(async valid => {
                if (valid) {
                    let res;

                    // 显示对话框loading
                    this.showLoading = true;

                    // 处理过期时间格式
                    if (this.formData.expire_time instanceof Date) {
                        this.formData.expire_time = Math.floor(this.formData.expire_time.getTime() / 1000);
                    }

                    switch (this.mode) {
                        case 'add':
                            // 添加接口
                            res = await this.$API.tenant.manage.add.post(this.formData);
                            break;
                        case 'edit':
                            // 编辑接口
                            res = await this.$API.tenant.manage.edit.post(this.formData);
                            break;
                    }

                    if (res.status === 1) {
                        // 保存成功时触发父窗口中的回调方法
                        this.$emit('saveSuccessEmit', this.formData, this.mode);

                        // 只在成功时关闭窗口
                        this.visibleDialog = false;

                        // 返回成功提示
                        this.$message.success(res.message || '保存成功');
                    } else {
                        this.$message.error(`操作失败：${res.message} (CODE:${res.code})`);
                    }
                    this.showLoading = false;
                }
            });
        }
    }
};
</script>

<style></style>
