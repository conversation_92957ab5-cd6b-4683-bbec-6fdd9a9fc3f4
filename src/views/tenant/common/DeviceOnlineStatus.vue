<template>
    <div class="device-online-status">
        <el-tooltip :content="tooltipContent" placement="top">
            <el-tag
                :type="getStatusType"
                :effect="effect"
                size="small"
                class="status-tag"
            >
                {{ getStatusText }}
                <i v-if="!mqttConnected" class="el-icon-warning-outline" style="margin-left: 2px;"></i>
            </el-tag>
        </el-tooltip>
    </div>
</template>

<script>
import loggerFactory from '@/utils/logger';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';
import deviceStatusUtils from '@/utils/deviceStatusUtils';

// 创建自定义前缀的日志记录器
const logger = loggerFactory.createLogger('DeviceStatus');

export default {
    name: 'DeviceOnlineStatus',
    props: {
        deviceId: {
            type: [Number, String],
            required: true
        },
        effect: {
            type: String,
            default: 'light',
            validator: value => ['light', 'dark', 'plain'].includes(value)
        },
        // 后端返回的初始在线状态
        initialOnline: {
            type: Boolean,
            default: null
        }
    },
    setup() {
        // 获取 Pinia store
        const deviceStore = useDeviceOnlineStore();

        return {
            deviceStore
        };
    },
    data() {
        return {
            // 保留一些本地状态用于事件发射
            lastEmittedOnlineStatus: null
        };
    },
    computed: {
        // 从 Pinia store 获取设备状态
        deviceStatus() {
            return this.deviceStore.getDeviceStatus(String(this.deviceId));
        },

        // 设备是否在线
        online() {
            return this.deviceStatus.online;
        },

        // 最后心跳时间
        lastHeartbeat() {
            return this.deviceStatus.lastHeartbeat;
        },

        // 最后更新时间
        lastUpdate() {
            return this.deviceStatus.lastUpdate;
        },

        // MQTT连接状态
        mqttConnected() {
            return this.deviceStore.mqttConnected;
        },

        tooltipContent() {
            return deviceStatusUtils.getHeartbeatTooltip(this.lastHeartbeat, this.mqttConnected);
        },

        // 获取状态类型
        getStatusType() {
            return deviceStatusUtils.getStatusType(this.online, this.mqttConnected);
        },

        // 获取状态文本
        getStatusText() {
            return deviceStatusUtils.getStatusText(this.online, this.mqttConnected);
        }
    },
    watch: {
        // 监听在线状态变化，发射事件给父组件
        online(newOnline, oldOnline) {
            if (newOnline !== oldOnline && this.lastEmittedOnlineStatus !== newOnline) {
                this.lastEmittedOnlineStatus = newOnline;
                logger.debug(`设备 ${this.deviceId} 状态变化: ${newOnline ? '在线' : '离线'}`);

                this.$emit('online-status-change', {
                    deviceId: this.deviceId,
                    online: newOnline,
                    lastHeartbeat: this.lastHeartbeat
                });
            }
        }
    },
    mounted() {
        const deviceIdStr = String(this.deviceId);

        // 如果有后端返回的初始在线状态，使用强制更新模式
        if (this.initialOnline !== null && this.initialOnline !== undefined) {
            const existingStatus = this.deviceStore.getDeviceStatus(deviceIdStr);

            if (!existingStatus) {
                // 如果没有现有状态，正常初始化
                this.deviceStore.initDeviceStatus(deviceIdStr, this.initialOnline);
                logger.debug(`设备 ${this.deviceId} 初始化在线状态: ${this.initialOnline ? '在线' : '离线'}`);
            } else {
                // 如果有现有状态，使用强制更新（设备列表刷新场景）
                this.deviceStore.initDeviceStatus(deviceIdStr, this.initialOnline, true);
                logger.debug(`设备 ${this.deviceId} 强制更新在线状态: ${this.initialOnline ? '在线' : '离线'} (API数据)`);
            }
        }

        // 设置初始发射状态
        this.lastEmittedOnlineStatus = this.online;
    }
};
</script>

<style scoped>
.device-online-status {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.status-tag {
    min-width: 42px;
    text-align: center;
}
</style>
