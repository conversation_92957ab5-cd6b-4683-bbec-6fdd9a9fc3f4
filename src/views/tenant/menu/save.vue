<template>
    <el-row :gutter="40" class="node-manager">
        <el-col v-if="!formData.id">
            <el-empty :image-size="100" description="请选择左侧菜单后操作"></el-empty>
        </el-col>
        <template v-else>
            <el-col :lg="12">
                <h2>节点编辑</h2>
                <el-form
                    ref="dialogForm"
                    :rules="rules"
                    :model="formData"
                    @keyup.enter="save"
                    label-position="left"
                    label-width="80px"
                >
                    <el-form-item label="节点名称" prop="title">
                        <el-input v-model="formData.title" clearable placeholder="节点显示名字"></el-input>
                    </el-form-item>
                    <el-form-item label="上级节点" prop="pid">
                        <el-cascader
                            v-model="formData.pid"
                            :options="menuOptions"
                            :props="menuProps"
                            :show-all-levels="false"
                            clearable
                            disabled
                            placeholder="顶级节点"
                        ></el-cascader>
                    </el-form-item>
                    <el-form-item label="节点类型" prop="type">
                        <el-radio-group v-model="formData.type">
                            <el-radio-button value="menu">菜单</el-radio-button>
                            <el-radio-button value="button">按钮</el-radio-button>
                            <el-radio-button value="iframe">Iframe</el-radio-button>
                            <el-radio-button value="link">外链</el-radio-button>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="节点标识" prop="name">
                        <el-input v-model="formData.name" clearable placeholder="节点标识"></el-input>
                        <div class="el-form-item-msg">
                            系统唯一且与内置组件名一致，否则导致缓存失效。如类型为Iframe的菜单，节点标识将代替源地址显示在地址栏
                        </div>
                    </el-form-item>
                    <el-form-item label="节点图标" prop="icon">
                        <sc-icon-select v-model="formData.icon" clearable></sc-icon-select>
                    </el-form-item>
                    <el-form-item label="节点路由" prop="path">
                        <el-input v-model="formData.path" clearable placeholder=""></el-input>
                        <div class="el-form-item-msg">前端访问路由地址</div>
                    </el-form-item>
                    <el-form-item label="模板视图" prop="component">
                        <el-input v-model="formData.component" clearable placeholder="">
                            <template #prepend>views/</template>
                        </el-input>
                        <div class="el-form-item-msg">如父节点、链接或Iframe等没有视图的菜单不需要填写</div>
                    </el-form-item>
                    <el-form-item label="重 定 向" prop="redirect">
                        <el-input v-model="formData.redirect" clearable placeholder=""></el-input>
                        <div class="el-form-item-msg">不需要时留空即可</div>
                    </el-form-item>
                    <el-form-item label="菜单高亮" prop="active">
                        <el-input v-model="formData.active" clearable placeholder=""></el-input>
                        <div class="el-form-item-msg">子节点或详情页需要高亮的上级菜单路由地址</div>
                    </el-form-item>
                    <el-form-item label="节点颜色" prop="color">
                        <el-color-picker v-model="formData.color" :predefine="predefineColors"></el-color-picker>
                    </el-form-item>
                    <el-form-item label="是否隐藏" prop="is_show">
                        <el-switch
                            v-model="formData.is_show"
                            :active-value="1"
                            active-text="显示"
                            :inactive-value="0"
                            inactive-text="隐藏"
                        />
                        <div class="el-form-item-msg">菜单不显示在导航中，但用户依然可以访问，例如详情页</div>
                    </el-form-item>
                    <el-form-item label="禁用启用" prop="status">
                        <el-switch
                            v-model="formData.status"
                            :active-value="1"
                            active-text="启用"
                            :inactive-value="0"
                            inactive-text="禁用"
                        />
                        <!--<div class="el-form-item-msg"></div>-->
                    </el-form-item>
                    <el-form-item label="整页路由" prop="fullpage">
                        <el-switch
                            v-model="formData.fullpage"
                            :active-value="1"
                            active-text="是"
                            :inactive-value="0"
                            inactive-text="否"
                        />
                    </el-form-item>
                    <el-form-item label="标签" prop="tag">
                        <el-input v-model="formData.tag" clearable placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            v-auths="['tenant.menu.add', 'tenant.menu.edit']"
                            :loading="loading"
                            type="primary"
                            icon="el-icon-circle-check-filled"
                            color="#1C409A"
                            @click="save"
                            >保 存</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col :lg="12" class="apilist">
                <h2>接口权限</h2>
                <el-alert :closable="false" effect="light" show-icon type="warning">
                    <template #title>
                        <span
                            >接口权限为当前菜单对应的页面默认拥有的接口列表，不包括具体的按钮操作接口、内页接口等。</span
                        >
                        <p> <b>接口名称：</b><span>必填，20个字符内。只做标记用，无其它用途。</span> </p>
                        <p> <b>接口地址：</b><span>必填，255个字符内。当前接口请求的服务端路由地址</span> </p>
                    </template>
                </el-alert>
                <sc-form-table
                    v-model="formData.apiList"
                    :addTemplate="apiListAddTemplate"
                    placeholder="暂无匹配接口权限"
                    @keyup.enter="save"
                >
                    <el-table-column label="接口名称" prop="name" width="150">
                        <template #default="scope">
                            <el-input v-model="scope.row.name" placeholder="请输入接口名称"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="接口地址" prop="url">
                        <template #default="scope">
                            <el-input v-model="scope.row.url" placeholder="请输入接口地址"></el-input>
                        </template>
                    </el-table-column>
                </sc-form-table>
            </el-col>
        </template>
    </el-row>
</template>

<script>
import scIconSelect from '@/components/scIconSelect';

export default {
    components: {
        scIconSelect
    },
    props: {
        menu: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            mode: 'add',
            formData: {
                id: '',
                pid: '',
                title: '',
                name: '',
                path: '',
                redirect: '',
                component: '',
                active: '',
                icon: '',
                color: '',
                type: 'menu',
                is_show: 1,
                fullpage: 0,
                tag: '',
                apiList: []
            },
            menuOptions: [],
            menuProps: {
                value: 'id',
                label: 'title',
                checkStrictly: true
            },
            predefineColors: ['#ff4500', '#ff8c00', '#ffd700', '#67C23A', '#00ced1', '#409EFF', '#c71585'],
            rules: {
                title: [{ required: true, message: '请输入节点名称', trigger: 'change' }],
                name: [{ required: true, message: '请输入节点标识' }]
            },
            apiListAddTemplate: {
                name: '',
                url: ''
            },
            loading: false,
            isModified: false, // 添加状态标记
            originalFormData: null, // 用于存储初始数据
            editCache: new Map() // 添加缓存存储
        };
    },
    watch: {
        menu: {
            handler() {
                this.menuOptions = this.treeToMap(this.menu);
            },
            deep: true
        },
        // 监听表单数据变化,并与初始数据比较
        formData: {
            handler(val) {
                if (this.formData.id && this.originalFormData) {
                    // 检查是否有实际改动
                    const hasChanges = JSON.stringify(val) !== JSON.stringify(this.originalFormData);
                    if (this.isModified !== hasChanges) {
                        this.isModified = hasChanges;
                        this.$emit('dataChanged', hasChanges);
                    }
                }
            },
            deep: true
        }
    },
    mounted() {},
    methods: {
        /**
         * 表单注入数据
         * @param data
         * @param pid
         */
        setData(data, pid) {
            const currentId = this.formData.id;
            // 如果当前有修改，保存到缓存
            if (this.isModified && currentId) {
                this.editCache.set(currentId, {
                    formData: JSON.parse(JSON.stringify(this.formData)),
                    originalFormData: JSON.parse(JSON.stringify(this.originalFormData))
                });
            }

            // 检查新节点是否有缓存的编辑状态
            if (data.id && this.editCache.has(data.id)) {
                const cache = this.editCache.get(data.id);
                this.formData = cache.formData;
                this.originalFormData = cache.originalFormData;
                this.isModified = true;
                this.$emit('dataChanged', true);
            } else {
                this.formData = JSON.parse(JSON.stringify(data));
                this.formData.apiList = data.apiList || [];
                this.formData.pid = pid;
                this.originalFormData = JSON.parse(JSON.stringify(this.formData));
                this.isModified = false;
                this.$emit('dataChanged', false);
            }
        },
        /**
         * 表单保存
         * @returns {Promise<void>}
         */
        async save() {
            this.loading = true;

            // 验证apiList接口权限是否为空以及相关操作
            let _apiList_failed = false;
            if (this.formData.apiList.length > 0) {
                this.formData.apiList.forEach((item, index) => {
                    if (item.name === '' && item.url === '') {
                        this.formData.apiList.splice(index, 1);

                        if (this.formData.apiList.length === 0) {
                            this.formData.apiList = [];
                        }

                        return false;
                    }

                    if (item.name === '') {
                        _apiList_failed = true;
                        this.$message.error('您填写了接口权限，第' + (index + 1) + '个接口中的接口名称不能为空');
                        return false;
                    }

                    if (item.url === '') {
                        _apiList_failed = true;
                        this.$message.error('您填写了接口权限，第' + (index + 1) + '个接口中的接口地址不能为空');
                        return false;
                    }
                });
            }

            // 接口权限验证失败时
            if (_apiList_failed) {
                this.loading = false;
                return false;
            }

            // 提交数据
            const res = await this.$API.tenant.menu.edit.post(this.formData);
            this.loading = false;
            if (res.status === 1) {
                this.$message.success('保存成功');
                this.isModified = false;
                this.originalFormData = JSON.parse(JSON.stringify(this.formData)); // 更新初始数据
                // 保存成功后清除该节点的缓存
                this.editCache.delete(this.formData.id);
                this.$emit('dataChanged', false);
                // 发送保存成功事件，携带更新后的数据
                this.$emit('saveSuccess', this.formData);
            } else {
                this.$message.warning(res.message);
            }
        },
        /**
         * 简单化菜单
         * @param tree
         * @returns {*[]}
         */
        treeToMap(tree) {
            const map = [];
            tree.forEach(item => {
                const obj = {
                    id: item.id,
                    pid: item.pid,
                    title: item.title,
                    children: item.children && item.children.length > 0 ? this.treeToMap(item.children) : null
                };
                map.push(obj);
            });
            return map;
        },
        /**
         * 清除当前节点的缓存
         */
        clearNodeCache() {
            if (this.formData.id) {
                this.editCache.delete(this.formData.id);
                this.isModified = false;
                this.$emit('dataChanged', false);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
h2 {
    font-size: 17px;
    color: var(--el-text-color-secondary);
    padding: 0 0 30px 0;
}

.apilist {
    border-left: 1px solid var(--el-border-color);

    ::v-deep(.el-alert) {
        margin-bottom: 25px;
        line-height: 2.5;
        padding-bottom: 15px;
    }

    ::v-deep(.el-alert p) {
        line-height: 2;
    }

    ::v-deep(.el-alert .el-icon) {
        --el-alert-icon-size: 42px !important;
    }
}

[data-theme='dark'] h2 {
    color: #ffffff;
}
</style>
