<template>
    <el-container>
        <el-aside v-loading="menuLoading" width="300px">
            <el-container>
                <el-header>
                    <el-input v-model="menuFilterText" clearable placeholder="输入关键字进行过滤"></el-input>
                </el-header>
                <el-main class="tree-container p0">
                    <el-tree
                        class="menu"
                        ref="menu"
                        node-key="id"
                        :check-strictly="true"
                        :data="menuList"
                        :default-expand-all="true"
                        :draggable="true"
                        :expand-on-click-node="false"
                        :filter-node-method="menuFilterNode"
                        :highlight-current="true"
                        :props="menuProps"
                        :show-checkbox="true"
                        @node-click="menuClick"
                        @node-drop="nodeDrop"
                    >
                        <template #default="{ node, data }">
                            <span class="el-tree-node__label">
                                <div class="label">
                                    <el-icon><component :is="data.icon || 'el-icon-document'" /></el-icon>
                                    <el-badge is-dot :hidden="!data.isModified">
                                        <span :class="{ 'node-modified': data.isModified }">{{ node.label }}</span>
                                    </el-badge>
                                </div>
                                <span class="handle">
                                    <el-tooltip effect="dark" placement="right">
                                        <template #content>
                                            <span>添加子节点</span>
                                        </template>
                                        <el-icon v-auths="['tenant.menu.add']" @click.stop="add(node, data)"
                                            ><el-icon-plus
                                        /></el-icon>
                                        <!--<el-button
                                            icon="el-icon-plus"
                                            size="small"
                                            v-auths="['tenant.menu.add']"
                                            @click.stop="add(node, data)"
                                        ></el-button>-->
                                    </el-tooltip>
                                </span>
                            </span>
                        </template>
                    </el-tree>
                </el-main>
                <el-footer style="height: 51px">
                    <el-button
                        v-auths="['tenant.menu.add']"
                        icon="el-icon-plus"
                        size="small"
                        type="primary"
                        @click="add()"
                        color="#1C409A"
                        >添加节点</el-button
                    >
                    <el-button
                        v-auths="['tenant.menu.delete']"
                        icon="el-icon-delete"
                        plain
                        size="small"
                        type="danger"
                        @click="delMenu"
                        >删除</el-button
                    >
                </el-footer>
            </el-container>
        </el-aside>
        <el-main ref="main" class="p0" style="padding: 20px">
            <save ref="save" :menu="menuList" @dataChanged="handleDataChanged" @saveSuccess="handleSaveSuccess"></save>
        </el-main>
    </el-container>
</template>

<script>
let newMenuIndex = 1;
import save from './save';

export default {
    name: 'tenant.menu.list',
    components: {
        save
    },
    data() {
        return {
            menuLoading: false,
            menuList: [],
            menuProps: {
                label: data => {
                    return data.title;
                }
            },
            menuFilterText: '',
            previousNodeId: null // 添加previousNodeId用于存储上一个选中的节点ID
        };
    },
    watch: {
        menuFilterText(val) {
            this.$refs.menu.filter(val);
        }
    },
    mounted() {
        this.getMenu();
    },
    methods: {
        /**
         * 加载树数据
         * @returns {Promise<void>}
         */
        async getMenu() {
            this.menuLoading = true;
            const res = await this.$API.tenant.menu.list.get();
            this.menuLoading = false;
            this.menuList = res.data;
        },
        /**
         * 树点击
         * @param data
         * @param node
         */
        async menuClick(data, node) {
            // 从 save 组件获取当前编辑状态
            const hasUnsavedChanges = this.$refs.save.isModified;
            const menu = this.$refs.menu;

            // 比较目标节点ID和之前保存的节点ID
            if (hasUnsavedChanges && this.previousNodeId) {
                // 如果点击的是其他节点才显示提示
                if (data.id !== this.previousNodeId) {
                    try {
                        await this.$confirm('当前节点有未保存的修改，切换后将丢失之前的编辑，是否继续切换？', '提示', {
                            confirmButtonText: '继续',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });
                        // 清除之前节点的修改状态
                        const previousNode = menu.getNode(this.previousNodeId);
                        if (previousNode) {
                            previousNode.data.isModified = false;
                        }
                        // 用户确认切换，清除当前节点的缓存
                        this.$refs.save.clearNodeCache();
                    } catch (err) {
                        // 用户取消切换，将树的当前节点设置回原节点
                        // 添加安全检查
                        if (menu && this.previousNodeId) {
                            menu.setCurrentKey(this.previousNodeId);
                        }
                        return;
                    }
                } else {
                    // 如果是点击当前节点，直接返回
                    return;
                }
            }

            // 更新previousNodeId为当前选中的节点
            this.previousNodeId = data.id;

            const pid = node.level === 1 ? 0 : node.parent.data.id;
            this.$refs.save.setData(data, pid);
            this.$refs.main.$el.scrollTop = 0;
        },
        /**
         * 树过滤
         * @param value
         * @param data
         * @returns {boolean}
         */
        menuFilterNode(value, data) {
            if (!value) return true;
            const targetText = data.title;
            return targetText.indexOf(value) !== -1;
        },
        /**
         * 树拖拽
         * @param draggingNode
         * @param dropNode
         * @param dropType
         */
        nodeDrop(draggingNode, dropNode, dropType) {
            this.$refs.save.setData({});
            this.$message(
                `拖拽对象：${draggingNode.data.title}, 释放对象：${dropNode.data.title}, 释放对象的位置：${dropType}`
            );
        },
        /**
         * 增加菜单
         * @param node
         * @param data
         * @returns {Promise<void>}
         */
        async add(node, data) {
            this.menuLoading = true;

            this.$confirm(`确定要添加新节点吗？`, '提示', {
                type: 'warning'
            })
                .then(async () => {
                    const _newMenuIndex = newMenuIndex++;
                    const newMenuName = '未命名' + _newMenuIndex;

                    const addType = data && data.id ? 'button' : 'menu';

                    const newMenuData = {
                        pid: data ? data.id : 0,
                        name: 'Controller' + _newMenuIndex + '.action',
                        path: '',
                        component: '',
                        is_show: 1,
                        title: newMenuName,
                        icon: '',
                        type: addType
                        //type: 'menu'
                    };

                    const res = await this.$API.tenant.menu.add.post(newMenuData);

                    if (res.status === 1) {
                        this.$message.success(res.message);
                        newMenuData.id = res.data.id;

                        this.$refs.menu.append(newMenuData, node);
                        this.$refs.menu.setCurrentKey(newMenuData.id);
                        const pid = node ? node.data.id : 0;
                        this.$refs.save.setData(newMenuData, pid);
                    } else {
                        this.$message.warning(res.message);
                    }
                })
                .catch(() => {});

            this.menuLoading = false;
        },
        /**
         * 删除菜单
         * @returns {Promise<boolean>}
         */
        async delMenu() {
            const CheckedNodes = this.$refs.menu.getCheckedNodes();

            if (CheckedNodes.length === 0) {
                this.$message.warning('请选择需要删除的项');
                return false;
            }

            const confirm = await this.$confirm('确认删除已选择的菜单吗？如果删除项中含有子集将会被一并删除', '提示', {
                type: 'warning',
                confirmButtonText: '删除',
                confirmButtonClass: 'el-button--danger'
            }).catch(() => {});

            if (confirm !== 'confirm') {
                return false;
            }

            this.menuLoading = true;
            const reqData = {
                id: CheckedNodes.map(item => item.id)
            };
            const res = await this.$API.tenant.menu.delete.post(reqData);
            this.menuLoading = false;

            if (res.status === 1) {
                CheckedNodes.forEach(item => {
                    this.$message.success(res.message);
                    const node = this.$refs.menu.getNode(item);
                    if (node.isCurrent) {
                        this.$refs.save.setData({});
                    }
                    this.$refs.menu.remove(item);
                });
            } else {
                this.$message.warning(res.message);
            }
        },
        /**
         * 数据变更处理
         * @param isChanged
         */
        handleDataChanged(isChanged) {
            const currentNode = this.$refs.menu.getCurrentNode();
            if (currentNode) {
                currentNode.isModified = isChanged;
            }
        },
        /**
         * 处理保存成功后的数据更新
         * @param updatedData
         */
        handleSaveSuccess(updatedData) {
            const menu = this.$refs.menu;
            if (!menu) return;

            // 获取当前节点
            const node = menu.getNode(updatedData.id);
            if (!node) return;

            // 更新节点数据
            node.data = { ...node.data, ...updatedData };
        }
    }
};
</script>

<style lang="scss" scoped>
.tree-container::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}

:deep(.el-badge__content.is-dot) {
    right: 0;
}

.node-modified {
    font-weight: bold;
    color: var(--el-color-primary);
}
</style>
