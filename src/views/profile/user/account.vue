<template>
    <el-card header="个人信息" shadow="never">
        <el-alert class="el-alert-tips" type="info" :show-icon="false" :closable="false">
            <template #title>
                <el-icon><el-icon-info-filled /></el-icon>
                <div class="el-alert--content">
                    <h2 class="el-alert__title">友情提示：</h2>
                    <hr />
                    <p class="el-alert__description">请完善以下信息，方便系统根据您的信息更好的为您服务。</p>
                </div>
            </template>
        </el-alert>
        <el-form
            ref="userForm"
            :model="userInfo"
            :rules="rules"
            @keyup.enter="saveUserInfo"
            label-position="right"
            label-width="100px"
            style="margin-top: 20px"
        >
            <el-form-item label="账号">
                <el-input v-model="userInfo.username" disabled></el-input>
                <div class="el-form-item-msg">用户账号用于登录，暂不支持修改</div>
            </el-form-item>
            <el-form-item label="名称">
                <el-input v-model="userInfo.realname" :disabled="userInfo.username === 'root'"></el-input>
            </el-form-item>
            <el-form-item label="手机">
                <el-input v-model="userInfo.phone" :disabled="!allowChangePhone">
                    <template #append>
                        <el-button type="primary" @click="changePhone">修改</el-button>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item label="邮箱">
                <el-input v-model="userInfo.email" :disabled="!allowChangeEmail">
                    <template #append>
                        <el-button type="primary" @click="changeEmail">修改</el-button>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item label="性别">
                <el-select v-model="userInfo.gender" placeholder="请选择">
                    <el-option v-for="(item, index) in genderObj" :key="index" :label="item" :value="index"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="生日">
                <el-date-picker
                    v-model="userInfo.birthday"
                    type="date"
                    placeholder="请选择您的出生日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    size="default"
                />
            </el-form-item>
            <el-form-item label="签名">
                <el-input
                    v-model="userInfo.signature"
                    placeholder="这个人很懒，什么都没留下。"
                    type="textarea"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" :loading="loading" @click="saveUserInfo">保存</el-button>
            </el-form-item>
        </el-form>
    </el-card>
</template>

<script>
import account from '@/utils/account';
import { useGlobalStore } from '@/stores/global';

const globalStore = useGlobalStore();

export default {
    data() {
        return {
            loading: false,
            allowChangePhone: false, // 可以修改手机号
            allowChangeEmail: false, // 可以修改邮箱
            userInfo: {
                username: 'Anonymous',
                realname: '匿名用户',
                phone: '',
                email: '',
                gender: '1',
                birthday: '1949-10-1',
                avatar: 'img/avatar-m.svg'
            },
            genderObj: {
                0: '女',
                1: '男',
                2: '保密'
            },
            rules: {
                realname: [{ required: true, message: '请输入您的称呼' }],
                phone: [{ required: true, message: '请填写您的手机号' }],
                email: [{ required: true, message: '请填写您的邮箱号' }],
                gender: [{ required: true, message: '请选择您的性别' }],
                birthday: [{ required: true, message: '请填写您的出生日期' }]
            }
        };
    },
    mounted() {
        // 从缓存中取出用户信息
        this.userInfo = account.userInfo.get() || {};

        // 根据性别获取默认头像信息
        const avatar_info = account.getDefaultAvatarInfo(this.userInfo.gender);

        // 头像地址
        this.userInfo.avatar = avatar_info.url;
        // 性别class
        this.userInfo.gender_class = avatar_info.class;
    },
    methods: {
        /**
         * 修改手机号
         */
        changePhone() {
            this.$message.success('修改手机号');
            this.allowChangePhone = true;
        },
        /**
         * 修改邮箱号
         */
        changeEmail() {
            this.$message.success('修改邮箱');
            this.allowChangeEmail = true;
        },
        /**
         * 更新用户信息
         * @returns {Promise<void>}
         */
        async saveUserInfo() {
            const validate = await this.$refs.userForm.validate().catch(() => {});
            if (!validate) {
                return false;
            }

            this.loading = true;

            // 提交数据
            const res = await this.$API.account.edit.post(this.userInfo);

            if (res.status === 1) {
                // 更新用户缓存信息
                this.userInfo = account.userInfo.set(this.userInfo);

                // 设置全局触发修改信息
                globalStore.SET_userInfoLastUpdateTime(new Date().getTime());

                this.$message.success('更新成功');
            } else {
                this.$message.warning(res.message);
            }

            this.loading = false;
        }
    }
};
</script>

<style scoped lang="scss">
@use 'sass:color';

.el-alert-tips {
    // 主色调
    --el-alert-bg-color: #b9d9ff;
    --el-text-color-primary: #ffffff;
    --el-color-primary-light-9: #6c92c1;

    border-radius: 3px;
    display: table;

    :deep(.el-alert__title) {
        display: table;
        line-height: inherit;
        width: 100%;
    }

    .el-icon {
        display: table-cell;
        font-size: 64px;
        color: var(--el-text-color-primary);

        svg {
            position: relative;
            top: 7px;
            left: -5px;
        }
    }

    .el-alert--content {
        position: relative;
        display: table-cell;
        padding-left: 15px;
        bottom: 3px;
        vertical-align: middle;

        h2 {
            color: var(--el-color-primary-light-9);
            font-size: 15px;
            font-weight: 600;
            margin: 10px 0;
        }

        p {
            font-size: 15px;
            color: var(--el-color-primary-light-9);
        }

        hr {
            border: 0;
            border-top: 1px solid var(--el-color-primary-light-9);
            box-sizing: content-box;
            height: 0;
            margin: 10px 0;
            overflow: visible;
            width: 100%;
            opacity: 0.45;
        }
    }
}
</style>
