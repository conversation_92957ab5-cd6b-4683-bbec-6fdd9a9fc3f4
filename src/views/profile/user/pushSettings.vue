<template>
    <el-card header="事务待办" shadow="never">
        <el-form ref="form" :model="formData" label-position="left" label-width="180px" style="margin-top: 20px">
            <el-form-item label="消息通知">
                <el-checkbox v-model="formData.notice_sms_push">短信推送</el-checkbox>
                <el-checkbox v-model="formData.notice_wechat_push">微信推送</el-checkbox>
            </el-form-item>
            <el-form-item label="有新的待办">
                <el-checkbox v-model="formData.task_sms_push">短信推送</el-checkbox>
                <el-checkbox v-model="formData.task_wechat_push">微信推送</el-checkbox>
            </el-form-item>
        </el-form>
    </el-card>
</template>

<script>
export default {
    data() {
        return {
            formData: {
                notice_sms_push: true,
                notice_wechat_push: true,
                task_sms_push: true,
                task_wechat_push: true
            }
        };
    }
};
</script>

<style></style>
