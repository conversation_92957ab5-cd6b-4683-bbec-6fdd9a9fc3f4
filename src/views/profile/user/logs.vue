<template>
    <el-card header="操作日志" shadow="never">
        <user-log-list ref="logListRef"></user-log-list>
    </el-card>
</template>

<script>
import userLogList from '../logs/list';

export default {
    name: 'user-logs',
    components: {
        userLogList
    },
    mounted() {
        // 当前用户操作日志获取
        this.get_log_list();
    },
    methods: {
        /**
         * 当前用户操作日志获取
         */
        get_log_list() {
            this.$nextTick(() => {
                this.$refs.logListRef.getLogList({});
            });
        }
    }
};
</script>

<style></style>
