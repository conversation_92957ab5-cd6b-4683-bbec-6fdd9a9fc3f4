<template>
    <el-container class="page-user">
        <el-aside style="width: 240px">
            <el-container>
                <el-header style="height: auto; display: block">
                    <div class="user-info-top">
                        <el-avatar :size="70" :src="userInfo.avatar" :class="userInfo.gender_class"></el-avatar>
                        <h2>{{ userInfo.username }}</h2>
                        <p>
                            <el-tag effect="dark" round disable-transitions>{{ userInfo.realname }}</el-tag>
                        </p>
                    </div>
                </el-header>
                <el-main class="p0">
                    <el-menu :default-active="page" class="menu">
                        <el-menu-item-group v-for="group in menu" :key="group.groupName" :title="group.groupName">
                            <el-menu-item
                                v-for="item in group.list"
                                :key="item.component"
                                :index="item.component"
                                @click="openPage"
                            >
                                <el-icon v-if="item.icon"><component :is="item.icon" /></el-icon>
                                <template #title>
                                    <span>{{ item.title }}</span>
                                </template>
                            </el-menu-item>
                        </el-menu-item-group>
                    </el-menu>
                </el-main>
            </el-container>
        </el-aside>
        <el-main>
            <component :is="page" v-if="componentLoaded" />
            <el-skeleton :rows="3" v-else />
        </el-main>
    </el-container>
</template>

<script>
import { ref, defineAsyncComponent } from 'vue';
import account from '@/utils/account';
import { useGlobalStore } from '@/stores/global';

const globalStore = useGlobalStore();

export default {
    name: 'profile',
    components: {
        account: defineAsyncComponent({
            loader: () => import('./user/account'),
            loadingComponent: null,
            delay: 0,
            onError(error, retry, fail) {
                console.error('Error loading component:', error);
                fail();
            }
        }),
        setting: defineAsyncComponent(() => import('./user/setting')),
        pushSettings: defineAsyncComponent(() => import('./user/pushSettings')),
        password: defineAsyncComponent(() => import('./user/password')),
        space: defineAsyncComponent(() => import('./user/space')),
        logs: defineAsyncComponent(() => import('./user/logs')),
        upToEnterprise: defineAsyncComponent(() => import('./user/upToEnterprise'))
    },
    data() {
        return {
            menu: [
                {
                    groupName: '基本设置',
                    list: [
                        {
                            icon: 'el-icon-postcard',
                            title: '个人信息',
                            component: 'account'
                        },
                        {
                            icon: 'el-icon-lock',
                            title: '修改密码',
                            component: 'password'
                        },
                        {
                            icon: 'el-icon-bell',
                            title: '通知设置',
                            component: 'pushSettings'
                        },
                        {
                            icon: 'el-icon-operation',
                            title: '系统设置',
                            component: 'setting'
                        }
                    ]
                },
                {
                    groupName: '数据管理',
                    list: [
                        {
                            icon: 'el-icon-coin',
                            title: '存储空间',
                            component: 'space'
                        },
                        {
                            icon: 'el-icon-clock',
                            title: '操作日志',
                            component: 'logs'
                        }
                    ]
                },
                {
                    groupName: '升级账号',
                    list: [
                        {
                            icon: 'el-icon-office-building',
                            title: '升级为企业版',
                            component: 'upToEnterprise'
                        }
                    ]
                }
            ],
            userInfo: {
                username: 'Anonymous',
                realname: '匿名用户',
                gender: 1,
                gender_class: 'unknown',
                avatar: 'img/avatar-m.svg'
            },
            page: 'account',
            componentLoaded: false
        };
    },
    //路由跳转进来 判断from是否有特殊标识做特殊处理
    beforeRouteEnter(to, from, next) {
        next(vm => {
            if (from.is) {
                //删除特殊标识，防止标签刷新重复执行
                delete from.is;
                //执行特殊方法
                vm.$alert('路由跳转过来后含有特殊标识，做特殊处理', '提示', {
                    type: 'success',
                    center: true
                })
                    .then(() => {})
                    .catch(() => {});
            }
        });
    },
    computed: {
        // 判断全局是否更新了用户信息
        isUpdatedUserInfo() {
            return globalStore.userInfoLastUpdateTime;
        }
    },
    mounted() {
        // 查询获取一次最新的用户缓存信息
        this.handleUpdateUserInfoSuccess();
        this.componentLoaded = true;
    },
    watch: {
        /**
         * 监听全局是否更新了用户信息
         * @param thisTimer
         * @param lastTimer
         */
        isUpdatedUserInfo(thisTimer, lastTimer) {
            if (thisTimer !== lastTimer) {
                // 查询获取一次最新的用户缓存信息
                this.handleUpdateUserInfoSuccess();
            }
        }
    },
    methods: {
        openPage(item) {
            this.page = item.index;
        },
        /**
         * 子组件更新完用户信息后的触发
         */
        handleUpdateUserInfoSuccess() {
            // 查询获取一次最新的用户缓存信息
            this.userInfo = account.userInfo.get();

            // 根据性别获取默认头像信息
            const avatar_info = account.getDefaultAvatarInfo(this.userInfo.gender);

            // 头像地址
            this.userInfo.avatar = avatar_info.url;
            // 性别class
            this.userInfo.gender_class = avatar_info.class;
        }
    }
};
</script>
<style scoped lang="scss">
.el-avatar {
    padding: 5px;
    &.woman {
        background-color: #ff8989;
    }
    &.man {
        background-color: #1760c9;
    }
    &.unknown {
        background-color: #e5e5e5;
    }
}

.page-user {
    .user-info-top {
        text-align: center;

        h2 {
            font-size: 18px;
            margin-top: 5px;
            font-weight: 500;
        }

        p {
            margin: 8px 0 10px 0;
        }
    }

    .menu {
        background: none;

        .el-menu-item {
            --el-menu-item-height: 50px;
        }

        .el-menu-item-group {
            border-top: 1px solid var(--el-border-color-light);

            &:first-child {
                border: 0;
            }
        }

        .el-menu-item-group__title {
            font-size: 13px;
            color: #999999;
        }
    }
}
</style>
