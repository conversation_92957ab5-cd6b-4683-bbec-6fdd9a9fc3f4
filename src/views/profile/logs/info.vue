<template>
    <el-main style="padding: 0 20px">
        <el-card shadow="never">
            <el-descriptions :column="1" border title="">
                <el-descriptions-item label="日志ID" min-width="150px">{{ logInfo.id }}</el-descriptions-item>
                <el-descriptions-item label="用户ID">{{ logInfo.user_id }}</el-descriptions-item>
                <el-descriptions-item label="用户名称">{{ logInfo.user_name }}</el-descriptions-item>
                <el-descriptions-item label="事件名称">{{ logInfo.event }}</el-descriptions-item>
                <el-descriptions-item label="事件路径">{{ logInfo.path }}</el-descriptions-item>
                <el-descriptions-item label="事件描述">
                    <pre>{{ logInfo.description }}</pre>
                </el-descriptions-item>
                <el-descriptions-item label="事件参数">
                    <pre>{{ logInfo.params }}</pre>
                </el-descriptions-item>
                <el-descriptions-item label="内存消耗">{{ logInfo.mem }}</el-descriptions-item>
                <el-descriptions-item label="吞吐量">{{ logInfo.reqs }}</el-descriptions-item>
                <el-descriptions-item label="响应时长">{{ logInfo.runtime }}</el-descriptions-item>
                <el-descriptions-item label="客户端IP">{{ logInfo.client_ip }}</el-descriptions-item>
                <el-descriptions-item label="发生时间">{{ logInfo.create_at }}</el-descriptions-item>
            </el-descriptions>
        </el-card>
    </el-main>
</template>

<script>
export default {
    name: 'log-info',
    data() {
        return {
            logInfo: {}
        };
    },
    methods: {
        setData(row) {
            this.logInfo = row;
            if (this.logInfo.params) {
                this.logInfo.params = this.$TOOL.jsonDecode(this.logInfo.params);
            }
            if (this.logInfo.description) {
                this.logInfo.description = this.$TOOL.jsonDecode(this.logInfo.description);
            }
        }
    }
};
</script>

<style scoped></style>
