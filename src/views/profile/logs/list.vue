<template>
    <el-header v-if="1 === 2">
        <div class="left-panel">
            <div class="right-panel-search">
                <el-input
                    style="min-width: 300px"
                    v-model="searchObj.keyword"
                    clearable
                    placeholder="登录账号 / 姓名 / 手机号 / 邮箱"
                    @keyup.enter="searchHandle"
                    @clear="searchHandle"
                ></el-input>
                <el-button icon="el-icon-search" type="primary" color="#1C409A" @click="searchHandle"></el-button>
            </div>
        </div>
        <div class="right-panel">
            <div class="right-panel-search">
                <el-input
                    style="min-width: 300px"
                    v-model="searchObj.keyword"
                    clearable
                    placeholder="登录账号 / 姓名 / 手机号 / 邮箱"
                    @keyup.enter="searchHandle"
                    @clear="searchHandle"
                ></el-input>
                <el-button icon="el-icon-search" type="primary" color="#1C409A" @click="searchHandle"></el-button>
            </div>
        </div>
    </el-header>
    <el-main class="p0">
        <scTable
            ref="logDataTable"
            :apiObj="logDataObj"
            :params="logDataParams"
            remoteFilter
            remoteSort
            row-key="id"
            stripe
            class="custom-list-table"
        >
            <el-table-column label="ID" prop="id" sortable="custom" width="80"></el-table-column>
            <el-table-column label="用户名称" show-overflow-tooltip prop="user.realname" width="120"></el-table-column>
            <el-table-column label="事件名称" show-overflow-tooltip prop="event" width="150"></el-table-column>
            <el-table-column label="事件路径" show-overflow-tooltip prop="path" width="200"></el-table-column>
            <el-table-column label="内存消耗" prop="mem" width="100"></el-table-column>
            <el-table-column label="吞吐量" prop="reqs" width="100"></el-table-column>
            <el-table-column label="响应时长" show-overflow-tooltip prop="runtime" width="100"></el-table-column>
            <el-table-column label="客户端IP" prop="client_ip" width="150"></el-table-column>
            <el-table-column label="发生时间" prop="create_at" sortable="custom" width="160"></el-table-column>
            <el-table-column align="center" fixed="right" label="操作" width="80">
                <template #default="scope">
                    <el-button size="small" type="success" @click="log_view(scope.row, scope.$index)" color="#28a745"
                        >详细</el-button
                    >
                    <!--<el-button size="small" type="danger" :disabled="true">删除</el-button>-->
                </template>
            </el-table-column>
        </scTable>
    </el-main>

    <!--日志详情-->
    <el-drawer
        v-model="info_dialog"
        :size="800"
        custom-class="drawerBG"
        destroy-on-close
        direction="rtl"
        title="日志详细信息"
    >
        <log-info ref="logInfoRef"></log-info>
    </el-drawer>
</template>

<script>
import account from '@/utils/account';
import logInfo from '../logs/info';

export default {
    name: 'user-log-list',
    components: {
        logInfo
    },
    data() {
        return {
            is_super: false,
            info_dialog: false,
            logDataObj: null,
            logDataParams: {},
            searchObj: {
                keyword: ''
            },
            userList: []
        };
    },
    created() {
        let userInfo = account.userInfo.get() || {};

        if (userInfo.username === 'root') {
            this.is_super = true;
        }
    },
    async mounted() {
        /*if (this.is_super) {
            this.is_super = true;

            // 获取所有用户列表for下拉筛选
            const res = await this.$API.system.user.selects.post();
            console.log(res);
        }*/
    },
    methods: {
        /**
         * 数据注入
         * @param user
         */
        getLogList(user) {
            this.logDataObj = this.$API.account.logs;

            if (user.id) {
                this.logDataParams.user_id = Number(user.id);
                //this.$refs.logDataTable.upData(this.logDataParams);
                //this.$refs.logDataTable.reload(this.logDataParams);
            }
        },
        /**
         * 职位详细信息
         * @param row
         */
        log_view(row) {
            this.info_dialog = true;
            this.$nextTick(() => {
                this.$refs.logInfoRef.setData(row);
            });
        },
        searchHandle() {
            //
        }
    }
};
</script>

<style scoped></style>
