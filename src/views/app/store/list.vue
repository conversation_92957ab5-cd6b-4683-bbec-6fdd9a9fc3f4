<template>
    <el-container>
        <el-header>
            <div class="right-panel">
                <div class="right-panel-search">
                    <el-input
                        v-model="searchObj.keyword"
                        clearable
                        placeholder="应用名称 / 应用描述"
                        @keyup.enter="searchHandle"
                        @clear="searchHandle"
                    ></el-input>
                    <el-button icon="el-icon-search" type="primary" color="#1C409A" @click="searchHandle"></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="main-container">
            <el-row :gutter="24">
                <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="(item, index) in appList" :key="index">
                    <el-card class="app-card" shadow="hover">
                        <img :src="item.icon" class="app-icon" @error="handleIconError" :onerror="`this.src='${defaultIcon}'`" />
                        <h3>{{ item.name }}</h3>
                        <p>{{ item.description }}</p>
                        <div class="app-info">
                            <span class="category">{{ item.category }}</span>
                            <div class="rating">
                                <el-rate
                                    v-model="item.rating"
                                    disabled
                                    text-color="#ff9900"
                                    score-template="{value}"
                                ></el-rate>
                                <span>{{ item.rating }}</span>
                            </div>
                        </div>
                        <div class="app-actions">
                            <el-button type="primary" @click="installApp(item)">安装</el-button>
                            <el-button @click="viewDetail(item)">详情</el-button>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </el-main>
    </el-container>
</template>

<script>
export default {
    name: 'app.store.list',
    data() {
        return {
            defaultIcon: '/img/icons/server-alert.svg',
            searchObj: {
                keyword: ''
            },
            appList: [
                {
                    id: 1,
                    name: 'Kubernetes管理器',
                    description: '一站式Kubernetes集群管理工具，支持多集群管理、应用部署、监控告警等功能',
                    icon: '/img/icons/server-alert.svg',
                    category: '容器编排',
                    version: 'v1.0.0',
                    rating: 4.5
                },
                {
                    id: 2,
                    name: 'Jenkins Pipeline',
                    description: '强大的CI/CD流水线工具，支持多种构建方式和自动化部署',
                    icon: '/img/icons/server-alert.svg',
                    category: '持续集成',
                    version: 'v2.1.0',
                    rating: 4.8
                },
                {
                    id: 3,
                    name: '日志分析平台',
                    description: '集中式日志收集和分析平台，支持多种日志格式，提供实时检索和告警功能',
                    icon: '/img/icons/server-alert.svg',
                    category: '日志管理',
                    version: 'v1.2.0',
                    rating: 4.3
                },
                {
                    id: 4,
                    name: '监控告警中心',
                    description: '统一的监控告警平台，支持多维度监控指标采集和灵活的告警策略配置',
                    icon: '/img/icons/server-alert.svg',
                    category: '监控告警',
                    version: 'v1.5.0',
                    rating: 4.6
                }
            ]
        };
    },
    methods: {
        searchHandle() {
            // 实现搜索逻辑
        },
        installApp(app) {
            // 实现应用安装逻辑
        },
        viewDetail(app) {
            // 实现查看详情逻辑
        },
        handleIconError(e) {
            e.target.src = this.defaultIcon;
        }
    }
};
</script>

<style lang="scss" scoped>
.main-container {
    padding: 20px;

    .el-row {
        margin: 0 -8px;
    }

    .el-col {
        padding: 0 8px;
        margin-bottom: 16px;
    }
}

.app-card {
    margin-bottom: 24px;
    transition: all 0.3s;
    border-radius: 8px;
    height: 100%;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    .el-card__body {
        padding: 24px;
        text-align: center;
        display: flex;
        flex-direction: column;
        height: 100%;
    }
}

.app-icon {
    width: 72px;
    height: 72px;
    margin-bottom: 16px;
    border-radius: 12px;
    padding: 12px;
    background-color: #f5f7fa;
    object-fit: contain;
}

h3 {
    margin: 0 0 12px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    line-height: 1.4;
}

p {
    margin: 0;
    font-size: 14px;
    color: #606266;
    line-height: 1.6;
    height: 44px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    flex: 1;
}

.app-info {
    margin: 16px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    color: #909399;

    .category {
        background-color: #f0f2f5;
        padding: 4px 12px;
        border-radius: 16px;
        font-weight: 500;
    }

    .rating {
        display: flex;
        align-items: center;

        .el-rate {
            display: inline-flex;
            margin-right: 6px;
            transform: scale(0.9);
            transform-origin: right;
        }
    }
}

.app-actions {
    margin-top: 24px;
    display: flex;
    gap: 12px;
    justify-content: center;

    .el-button {
        padding: 8px 24px;
        min-width: 88px;
    }
}
</style>
