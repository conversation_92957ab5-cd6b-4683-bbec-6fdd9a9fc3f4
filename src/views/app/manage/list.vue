<template>
    <el-container>
        <el-header>
            <div class="left-panel">
                <el-button
                    v-auths="['app.manage.uninstall']"
                    :disabled="selection.length === 0 || hasSystemApp()"
                    icon="el-icon-delete"
                    plain
                    type="danger"
                    @click="batchUninstall"
                    >批量卸载</el-button
                >
            </div>
            <div class="right-panel">
                <div class="right-panel-search">
                    <el-input
                        v-model="searchObj.keyword"
                        clearable
                        placeholder="应用名称 / 应用描述"
                        @keyup.enter="searchHandle"
                        @clear="searchHandle"
                    ></el-input>
                    <el-button icon="el-icon-search" type="primary" color="#1C409A" @click="searchHandle"></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="p0">
            <scTable
                ref="appTable"
                :apiObj="appDataObj"
                row-key="id"
                @selection-change="selectionChange"
            >
                <el-table-column type="selection" width="50"></el-table-column>
                <el-table-column label="应用图标" width="80">
                    <template #default="scope">
                        <div v-if="!scope.row.icon" class="app-icon">
                            <el-icon :size="24"><Monitor /></el-icon>
                        </div>
                        <div v-else-if="isSvgString(scope.row.icon)" class="app-icon" v-html="parseSvgIcon(scope.row.icon)"></div>
                        <div v-else class="app-icon">
                            <el-image
                                :src="scope.row.icon"
                                :size="24"
                                fit="contain"
                                @error="handleIconError"
                            >
                                <template #error>
                                    <el-icon :size="24"><Monitor /></el-icon>
                                </template>
                            </el-image>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="应用名称" prop="name" width="160"></el-table-column>
                <el-table-column label="应用描述" prop="description"></el-table-column>
                <el-table-column label="版本" prop="version"></el-table-column>
                <el-table-column label="状态">
                    <template #default="scope">
                        <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                            {{ scope.row.status === 1 ? '运行中' : '已停止' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="应用类型" width="120">
                    <template #default="scope">
                        <el-tag v-if="scope.row.is_system === 1" type="success">系统应用</el-tag>
                        <el-tag v-else type="info">普通应用</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template #default="scope">
                        <el-button
                            v-auths888="['app.manage.start']"
                            type="success"
                            plain
                            size="small"
                            @click="startApp(scope.row)"
                            v-if="scope.row.status !== 1"
                            :disabled="scope.row.is_system === 1"
                        >启动</el-button>
                        <el-button
                            v-auths888="['app.manage.stop']"
                            type="warning"
                            plain
                            size="small"
                            @click="stopApp(scope.row)"
                            v-if="scope.row.status === 1"
                            :disabled="scope.row.is_system === 1"
                        >停止</el-button>
                        <el-button
                            v-auths888="['app.manage.upgrade']"
                            type="primary"
                            plain
                            size="small"
                            @click="upgradeApp(scope.row)"
                            v-if="scope.row.canUpgrade"
                            :disabled="scope.row.is_system === 1"
                        >升级</el-button>
                        <el-button
                            v-auths888="['app.manage.uninstall']"
                            type="danger"
                            plain
                            size="small"
                            @click="uninstallApp(scope.row)"
                            :disabled="scope.row.is_system === 1"
                        >卸载</el-button>
                    </template>
                </el-table-column>
            </scTable>
        </el-main>
    </el-container>
</template>

<script>
export default {
    name: 'app.manage.list',
    data() {
        return {
            searchObj: {
                keyword: ''
            },
            selection: [],
            appDataObj: this.$API.system.app.list
        };
    },
    methods: {
        isSvgString(icon) {
            return typeof icon === 'string' && icon.trim().startsWith('<svg')
        },
        parseSvgIcon(svgString) {
            // 移除宽高属性，添加viewBox（如果没有），设置固定大小
            return svgString
                .replace(/width="[^"]*"/g, '')
                .replace(/height="[^"]*"/g, '')
                .replace(/class="[^"]*"/g, 'class="app-icon-svg"')
                .replace(/<svg/, '<svg width="36" height="36"')
        },
        handleIconError() {
            // 图片加载失败时的处理
        },
        searchHandle() {
            this.$refs.appTable.upData();
        },
        selectionChange(selection) {
            this.selection = selection;
        },
        hasSystemApp() {
            return this.selection.some(app => app.is_system === 1);
        },
        /**
         * 启动应用
         */
        startApp(app) {
            // 系统应用检查
            if (app.is_system === 1) {
                this.$message.warning('系统应用不能操作');
                return;
            }
            
            // 实现启动应用逻辑
        },
        /**
         * 停止应用
         */
        stopApp(app) {
            // 系统应用检查
            if (app.is_system === 1) {
                this.$message.warning('系统应用不能操作');
                return;
            }
            
            // 实现停止应用逻辑
        },
        /**
         * 卸载单个应用
         */
        uninstallApp(app) {
            // 系统应用检查
            if (app.is_system === 1) {
                this.$message.warning('系统应用不能卸载');
                return;
            }
            
            // 实现卸载单个应用逻辑
        },
        /**
         * 升级应用
         */
        upgradeApp(app) {
            // 系统应用检查
            if (app.is_system === 1) {
                this.$message.warning('系统应用不能操作');
                return;
            }
            
            // 实现升级应用逻辑
        },
        batchUninstall() {
            // 再次检查是否包含系统应用
            if (this.hasSystemApp()) {
                this.$message.warning('系统应用不能卸载');
                return;
            }
            
            // 实现批量卸载应用逻辑
        }
    }
};
</script>

<style lang="scss" scoped>
.app-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #1C409A;
    height: 40px;

    :deep(.app-icon-svg) {
        width: 36px;
        height: 36px;
    }
}

.system-app-tip {
    color: #909399;
    font-size: 13px;
}

.el-table {
    :deep(.el-table__row.is-system) {
        background-color: #f5f7fa;
    }
}
</style>
