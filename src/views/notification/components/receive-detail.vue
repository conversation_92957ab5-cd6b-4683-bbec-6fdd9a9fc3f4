<template>
    <div class="message-detail">
        <div class="message-header">
            <h2 class="message-title">{{ message.title }}</h2>
            <div class="message-meta">
                <div class="meta-left">
                    <el-tag size="small" :type="getTypeTag(message.type)" class="type-tag">
                        {{ getTypeName(message.type) }}
                    </el-tag>
                    <span class="meta-divider">|</span>
                    <span class="meta-info">发送者: {{ message.sender_name || message.sender_id }}</span>
                </div>
                <div class="meta-right">
                    <span class="meta-info">{{ message.create_at }}</span>
                </div>
            </div>
        </div>

        <el-divider />

        <div class="message-content" v-html="message.content"></div>

        <el-divider />

        <div class="message-footer">
            <el-button-group>
                <el-button
                    :type="message.is_read ? 'warning' : 'success'"
                    :icon="message.is_read ? 'el-icon-remove' : 'el-icon-check'"
                    size="small"
                    @click="message.is_read ? markUnread() : markRead()"
                >
                    {{ message.is_read ? '标为未读' : '标为已读' }}
                </el-button>
                <el-button :type="message.is_liked ? 'danger' : 'info'" size="small" @click="like">
                    <el-icon>
                        <el-icon-pointer :style="{ color: message.is_liked ? '#fff' : '' }" />
                    </el-icon>
                    <span class="like-count">{{ message.counter?.like_count || 0 }}</span>
                </el-button>
                <el-button :type="message.is_disliked ? 'warning' : 'info'" size="small" @click="dislike">
                    <el-icon>
                        <el-icon-pointer class="is-reverse" :style="{ color: message.is_disliked ? '#fff' : '' }" />
                    </el-icon>
                    <span class="dislike-count">{{ message.counter?.dislike_count || 0 }}</span>
                </el-button>
            </el-button-group>
        </div>
    </div>
</template>

<script>
import { getTypeTag, getTypeName } from '@/constants/notification';
import { useNotificationStore } from '@/stores/notification';
import NotificationUtils from '@/utils/notification';

export default {
    name: 'ReceiveMessageDetail',
    props: {
        message: {
            type: Object,
            required: true
        },
        // 添加更新列表方法的参数,由父组件传入
        updateListData: {
            type: Function,
            default: null
        }
    },
    emits: ['mark-read', 'mark-unread', 'like', 'status-changed', 'like-changed'],

    async mounted() {
        // 获取最新的互动状态
        await this.updateInteractStatus();

        // 如果消息未读,自动标记为已读
        if (this.message && !this.message.is_read) {
            await this.markRead();
        }
    },

    methods: {
        getTypeTag,
        getTypeName,

        /**
         * 标记消息已读
         */
        async markRead() {
            const success = await NotificationUtils.updateMessageStatus(this.message, true, this.updateListData);
            if (success) {
                this.$emit('status-changed', {
                    id: this.message.id,
                    is_read: true
                });
            }
        },

        /**
         * 标记消息未读
         */
        async markUnread() {
            const success = await NotificationUtils.updateMessageStatus(this.message, false, this.updateListData);
            if (success) {
                this.$emit('status-changed', {
                    id: this.message.id,
                    is_read: false
                });
            }
        },

        /**
         * 点赞操作
         */
        async like() {
            await NotificationUtils.handleInteraction('like', this.message, this.updateListData);
        },

        /**
         * 踩操作
         */
        async dislike() {
            await NotificationUtils.handleInteraction('dislike', this.message, this.updateListData);
        },

        /**
         * 更新互动状态
         */
        async updateInteractStatus() {
            const result = await NotificationUtils.getInteractStatus(this.message.id, this.message);
            if (!result.success) {
                console.error(result.message);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.message-detail {
    padding: 20px;

    .message-header {
        margin-bottom: 20px;

        .message-title {
            margin: 0 0 15px;
            font-size: 20px;
            font-weight: bold;
            color: var(--el-text-color-primary);
        }

        .message-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--el-text-color-secondary);
            font-size: 14px;

            .meta-left {
                display: flex;
                align-items: center;
                gap: 10px;

                .type-tag {
                    font-weight: normal;
                }

                .meta-divider {
                    color: var(--el-border-color-lighter);
                }
            }

            .meta-info {
                color: var(--el-text-color-secondary);
            }
        }
    }

    .message-content {
        padding: 20px 0;
        line-height: 1.8;
        font-size: 15px;
        color: var(--el-text-color-primary);

        ::v-deep(img) {
            max-width: 100%;
            height: auto;
        }

        ::v-deep(p) {
            margin: 1em 0;
        }
    }

    .message-footer {
        padding: 20px 0;
        display: flex;
        justify-content: flex-end;

        .like-count {
            margin-left: 5px;
        }

        .dislike-count {
            margin-left: 5px;
            min-width: 20px;
            display: inline-block;
        }
    }
}

.is-reverse {
    transform: rotate(180deg);
}
</style>
