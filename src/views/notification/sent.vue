<template>
    <el-container>
        <el-header>
            <div class="right-panel">
                <el-select
                    v-model="searchParams.type"
                    placeholder="全部类型"
                    style="width: 120px"
                    @change="handleSearch"
                    clearable
                    @clear="handleSearch"
                >
                    <el-option
                        v-for="item in MESSAGE_TYPE_OPTIONS"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
                <el-select
                    v-model="searchParams.receive_range"
                    placeholder="全部接收范围"
                    style="width: 140px"
                    @change="handleSearch"
                    clearable
                    @clear="handleSearch"
                >
                    <el-option
                        v-for="item in RECEIVE_RANGE_OPTIONS"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
                <el-input
                    v-model="searchParams.keyword"
                    clearable
                    placeholder="搜索消息内容"
                    style="width: 220px"
                    @keyup.enter="handleSearchDebounced"
                    @clear="handleSearchDebounced"
                ></el-input>
                <el-button type="primary" v-debounce="{ fn: handleSearch, delay: 300 }" color="#1C409A">搜索</el-button>
            </div>
        </el-header>
        <el-main class="p0">
            <scTable ref="messageTable" :apiObj="messageDataObj" :params="searchParams" row-key="id">
                <el-table-column label="标题" prop="title" min-width="200" show-overflow-tooltip />
                <el-table-column label="类型" prop="type" width="100">
                    <template #default="scope">
                        <el-tag :type="getTypeTag(scope.row.type)">
                            {{ getTypeName(scope.row.type) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="发送时间" prop="create_at" width="180" />
                <el-table-column label="接收范围" width="100">
                    <template #default="scope">
                        <el-tag :type="getRangeType(scope.row.receive_range)">
                            {{ getRangeName(scope.row.receive_range) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="120">
                    <template #default="scope">
                        <el-button @click="handleView(scope.row)" type="primary" link>查看</el-button>
                    </template>
                </el-table-column>
            </scTable>
        </el-main>
    </el-container>

    <el-drawer v-model="detailVisible" :size="600" title="消息详情" destroy-on-close>
        <sent-detail v-if="detailVisible" :message="currentMessage" />
    </el-drawer>
</template>

<script>
import {
    getTypeTag,
    getTypeName,
    getRangeTag,
    getRangeName,
    MESSAGE_TYPE_OPTIONS,
    RECEIVE_RANGE_OPTIONS
} from '@/constants/notification';
import SentDetail from './components/sent-detail.vue';
import { debounce } from '@/utils/throttleDebounce';

export default {
    name: 'notification-sent',
    components: {
        SentDetail
    },
    props: {
        type: {
            type: [Number, String],
            default: null
        }
    },
    data() {
        return {
            MESSAGE_TYPE_OPTIONS,
            RECEIVE_RANGE_OPTIONS, // 添加接收范围选项
            messageDataObj: this.$API.notification.sentList,
            searchParams: {
                type: null, // 使用null作为默认值
                receive_range: null, // 添加接收范围参数
                keyword: ''
            },
            detailVisible: false,
            currentMessageId: null,
            currentMessage: null
        };
    },
    watch: {
        type: {
            handler(newType) {
                console.log('当前type是：', newType);
                this.searchParams.type = newType;
                this.searchParams.keyword = null;
                this.searchParams.receive_range = null; // 重置接收范围
                this.handleSearch();
            },
            immediate: true
        }
    },
    created() {
        // 搜索防抖
        this.handleSearchDebounced = debounce(this.handleSearch, 300);
    },
    methods: {
        getTypeTag,
        getTypeName,
        getRangeType: getRangeTag,
        getRangeName,
        handleSearch() {
            this.$refs.messageTable?.upData(this.searchParams);
        },
        handleView(row) {
            this.currentMessage = row;
            this.detailVisible = true;
        }
    }
};
</script>

<style scoped lang="scss">
.right-panel-search .el-select {
    margin-right: 10px;
}
</style>
