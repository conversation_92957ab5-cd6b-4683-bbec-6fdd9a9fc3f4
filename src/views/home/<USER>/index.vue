<template>
    <div ref="widgets" class="widgets">
        <!--无部件时-->
        <div v-if="false" class="no-widgets">
            <el-empty image-size="260" description="没有部件啦" image="img/no-widgets.svg"></el-empty>
        </div>
        <!--服务器探针-->
        <server-info></server-info>
    </div>
</template>

<script>
import serverInfo from './components/serverInfo';

export default {
    components: {
        serverInfo
    },
    data() {
        return {};
    },
    created() {},
    mounted() {
        this.$emit('on-mounted');
    },
    computed: {},
    methods: {}
};
</script>

<style lang="scss" scoped></style>
