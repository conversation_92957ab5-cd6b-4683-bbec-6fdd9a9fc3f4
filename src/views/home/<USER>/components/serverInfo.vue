<template>
    <!--服务器信息-->
    <el-row :gutter="10">
        <el-col :span="8">
            <el-card shadow="never">
                <el-descriptions :column="1" border title="系统环境">
                    <el-descriptions-item v-for="(val, key) in server.app" :key="key" :label="key">{{
                        val
                    }}</el-descriptions-item>
                </el-descriptions>
            </el-card>
        </el-col>
        <el-col :span="8">
            <el-card class="clock-background" shadow="hover">
                <div class="time">
                    <p>{{ timeClock.day }}</p>
                    <h2>{{ timeClock.time }}</h2>
                </div>
            </el-card>
            <el-card shadow="never" style="margin-top: 10px">
                <el-descriptions :column="1" border title="php环境">
                    <el-descriptions-item v-for="(val, key) in server.php" :key="key" :label="key">
                        <span v-if="key !== 'status'">{{ val }}</span>
                        <div v-if="key === 'status'">
                            <sc-status-indicator
                                v-if="val === 1"
                                pulse
                                style="margin-right: 5px"
                                type="success"
                            ></sc-status-indicator>
                            <sc-status-indicator
                                v-if="val === 0"
                                pulse
                                style="margin-right: 5px"
                                type="danger"
                            ></sc-status-indicator>
                            <el-link v-if="val === 1" type="success">正常</el-link>
                            <el-link v-if="val === 0" type="danger">异常</el-link>
                        </div>
                    </el-descriptions-item>
                </el-descriptions>
            </el-card>
            <el-card shadow="never" style="margin-top: 10px">
                <el-descriptions :column="1" border title="mysql环境">
                    <el-descriptions-item v-for="(val, key) in server.mysql" :key="key" :label="key">
                        <span v-if="key !== 'status'">{{ val }}</span>
                        <div v-if="key === 'status'">
                            <sc-status-indicator
                                v-if="val === 1"
                                pulse
                                style="margin-right: 5px"
                                type="success"
                            ></sc-status-indicator>
                            <sc-status-indicator
                                v-if="val === 0"
                                pulse
                                style="margin-right: 5px"
                                type="danger"
                            ></sc-status-indicator>
                            <el-link v-if="val === 1" type="success">正常</el-link>
                            <el-link v-if="val === 0" type="danger">异常</el-link>
                        </div>
                    </el-descriptions-item>
                </el-descriptions>
            </el-card>
        </el-col>
        <el-col :span="8">
            <el-card shadow="never">
                <el-descriptions :column="1" border title="redis环境">
                    <el-descriptions-item v-for="(val, key) in server.redis" :key="key" :label="key">
                        <span v-if="key !== 'status'">{{ val }}</span>
                        <div v-if="key === 'status'">
                            <sc-status-indicator
                                v-if="val === 1"
                                pulse
                                style="margin-right: 5px"
                                type="success"
                            ></sc-status-indicator>
                            <sc-status-indicator
                                v-if="val === 0"
                                pulse
                                style="margin-right: 5px"
                                type="danger"
                            ></sc-status-indicator>
                            <el-link v-if="val === 1" type="success">正常</el-link>
                            <el-link v-if="val === 0" type="danger">异常</el-link>
                        </div>
                    </el-descriptions-item>
                </el-descriptions>
            </el-card>
        </el-col>
    </el-row>
</template>

<script>
import account from '@/utils/account';

export default {
    name: 'serverInfo',
    data() {
        return {
            // 计时器
            timer: null,
            interval: 30, // 刷新频率。单位：秒
            userInfo: {},
            timeClock: {
                time: '',
                day: ''
            },
            detail: {
                mysql: {
                    show_lock_tables: {},
                    show_status: {},
                    show_variables: {}
                },
                redis: {
                    info: {}
                }
            },
            server: {
                app: {},
                php: {},
                mysql: {},
                redis: {},
                elasticsearch: {}
            }
        };
    },
    created() {
        // 从缓存中取出用户信息
        this.userInfo = account.userInfo.get() || {};
    },
    mounted() {
        // 当前时间
        this.timer = new Date().getTime();

        // 获取当前环境的服务器状态
        this.getServerStatus();

        // 有监控页面，这里就不重新请求了
        if (this.userInfo) {
            // 每隔60秒检测一次服务状态
            this.timer = setInterval(() => {
                // 获取当前环境的服务器状态
                this.getServerStatus();
            }, this.interval * 1000);
        }

        // 获取当前时间
        this.showTimeClock();

        // 更新当前时间
        setInterval(() => {
            // 获取数据
            this.showTimeClock();
        }, 1000);

        this.$emit('on-mounted');
    },
    unmounted() {
        clearInterval(this.timer);
    },
    methods: {
        /**
         * 获取当前环境的服务器状态
         * @returns {Promise<void>}
         */
        async getServerStatus() {
            const res = await this.$API.monitor.service.get();

            if (res && res.status === 1) {
                const _data = res.data;

                if (_data.app) {
                    // 将详细的数据赋值上面
                    if (_data.mysql.show) {
                        this.detail.mysql.show_status = _data.mysql.show.status;
                        this.detail.mysql.show_variables = _data.mysql.show.variables;
                        this.detail.mysql.show_lock_tables = _data.mysql.show.lock_tables;

                        delete _data.mysql.show;
                    }

                    // 将详细的数据赋值上面
                    if (_data.redis.info) {
                        this.detail.redis = _data.redis.info;

                        delete _data.redis.info;
                    }

                    this.server.app = _data.app;
                    this.server.php = _data.php;
                    this.server.mysql = _data.mysql;
                    this.server.redis = _data.redis;
                }

                if (_data.php.status === 0) {
                    this.$message.warning('php ' + _data.php.message);
                }

                if (_data.mysql.status === 0) {
                    this.$message.warning('mysql ' + _data.mysql.message);
                }

                if (_data.redis.status === 0) {
                    this.$message.warning('redis ' + _data.redis.message);
                }
            } else {
                this.$message.error(res.message || '获取监控数据异常');
            }
        },
        /**
         * 时钟
         */
        showTimeClock() {
            this.timeClock.time = this.$TOOL.dateFormat(new Date() / 1000, 'hh:mm:ss');
            this.timeClock.day = this.$TOOL.dateFormat(new Date() / 1000, 'yyyy年MM月dd日');
        }
    }
};
</script>

<style scoped lang="scss">
:deep(.el-descriptions__body) {
    overflow: auto;
}

:deep(.el-card__header) {
    font-size: 15px;
    font-weight: 600;
}

.clock-background {
    //background: linear-gradient(90deg, #d6e1ff, #f2f2ff);
    background-color: #fff2e6;
    border: 1px solid #f6d7bf;
    border-radius: 0.25rem;
    color: #a27146;
    opacity: 0.6;

    .time {
        font-family:
            'PingFang SC',
            Lantinghei SC,
            Open Sans,
            'Hiragino Sans GB',
            'Microsoft YaHei',
            '微软雅黑',
            STXihei,
            '华文细黑',
            'Droid Sans',
            'WenQuanYi Micro Hei',
            'Helvetica Neue',
            'Helvetica',
            Tahoma,
            Arial,
            SimSun,
            '宋体',
            Heiti,
            '黑体',
            sans-serif;

        h2 {
            font-size: 39px;
            margin-top: 5px;
        }

        p {
            font-size: 14px;
        }
    }
}
</style>
