import { createApp } from 'vue';
import { createPinia } from 'pinia';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import frame from './frame';
import i18n from './locales';
import router from './router';
import App from './App.vue';

const app = createApp(App);

app.use(createPinia());
app.use(router);
app.use(ElementPlus);

app.use(i18n);
app.use(frame);

// 挂载app
app.mount('#app');
