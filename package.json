{"name": "devmix", "version": "1.6.9", "type": "module", "description": "基于VUE3和elementPlus实现的中后台前端解决方案", "author": "<EMAIL>", "license": "MIT", "private": true, "scripts": {"format": "prettier --write \"src/**/*.{vue,js,ts,json,html,scss}\"", "dev": "vite", "serve": "vite serve", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fingerprintjs/fingerprintjs": "^4.5.1", "@tinymce/tinymce-vue": "^5.1.1", "axios": "^1.7.9", "codemirror": "^6.65.7", "core-js": "^3.39.0", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "element-plus": "^2.9.1", "idb": "^8.0.3", "install": "^0.13.0", "marked": "^15.0.11", "mqtt": "^5.12.0", "nprogress": "^0.2.0", "pinia": "^2.3.0", "qrcodejs2": "^0.0.2", "qrcodejs2-fix": "^0.0.1", "sortablejs": "^1.15.2", "tinymce": "^6.8.3", "vue": "^3.5.13", "vue-i18n": "^11.1.2", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0", "xgplayer": "^3.0.11", "xgplayer-hls": "^3.0.11"}, "devDependencies": {"@rollup/plugin-terser": "^0.4.4", "@types/crypto-js": "^4.2.2", "@types/mqtt": "^0.0.34", "@types/node": "^22.15.29", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.0.4", "prettier": "^3.4.2", "sass": "^1.83.0", "sass-loader": "^16.0.4", "typescript": "^5.8.3", "vite": "^6.0.6", "vite-plugin-vue-devtools": "^7.6.8", "vue-tsc": "^2.2.10"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}